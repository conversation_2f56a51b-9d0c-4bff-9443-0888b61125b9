# -----------------------------
# PostgreSQL GUC configuration file
# -----------------------------
#
# This file consists of lines of the form:
# name|type|value_range|unit|message|
# Using '|' to split every value. Whitespace regards as useful information
# If you don't know the value of unit or message, please using NULL
# name: guc parameter name
# type: int, real, enum, bool, string
# value_range:
#    int        min_value,max_value
#    real       min_value,max_value
#    enum       value1,value2,value3...
#    bool       NULL
#    string     NULL
# Memory units:  kB = kilobytes        Time units:  ms  = milliseconds
#                MB = megabytes                     s   = seconds
#                GB = gigabytes                     min = minutes
#                                                   h   = hours
#                                                   d   = days
# message: only print relation parameter informaton
#
[coordinator/datanode]
enable_heap_prefetch|bool|0,0|NULL|Enable prefetch heap header|
enable_parse_fusion|bool|0,0|NULL|Enable parse fusion feature.|
max_imcs_cache|int|102400,2147483647|kB|NULL|
enable_parallel_populate|bool|0,0|NULL|NULL|
enable_imcsscan|bool|0,0|NULL|NULL|
htap_wait_xlog_lsn_timeout|int|0,2147483|s|NULL|
enable_borrow_memory|bool|0,0|NULL|NULL|
max_borrow_memory|int|0,2147483647|kB|NULL|
borrow_work_mem|int|0,2147483647|kB|This option is an extension of work_mem. For complex queries, each operator can use the amount of remote memory that this parameter is declared. The maximum expected remote memory usage is multiplied by the number of operators multiplied by the number of concurrency. |
htap_borrow_mem_percent|int|0,100|NULL|NULL|
lmemfabric_client_path|string|0,0|NULL|NULL|
enable_custom_parser|bool|0,0|NULL|NULL|
allocate_mem_cost|real|0,1.79769e+308|NULL|NULL|
cpu_index_tuple_cost|real|0,1.79769e+308|NULL|NULL|
cpu_operator_cost|real|0,1.79769e+308|NULL|NULL|
cpu_tuple_cost|real|0,1.79769e+308|NULL|NULL|
random_page_cost|real|0,1.79769e+308|NULL|NULL|
seq_page_cost|real|0,1.79769e+308|NULL|NULL|
alarm_component|string|0,0|NULL|NULL|
alarm_report_interval|int|0,2147483647|NULL|NULL|
allow_concurrent_tuple_update|bool|0,0|NULL|NULL|
enable_nvm|bool|0,0|NULL|NULL|
enable_huge_pages|bool|0,0|NULL|NULL|
enable_time_report|bool|0,0|NULL|NULL|
enable_batch_dispatch|bool|0,0|NULL|NULL|
allow_create_sysobject|bool|0,0|NULL|NULL|
allow_system_table_mods|bool|0,0|NULL|NULL|
application_name|string|0,0|NULL|NULL|
archive_command|string|0,0|NULL|NULL|
archive_dest|string|0,0|NULL|NULL|
archive_interval|int|1,1000|NULL|NULL|
archive_mode|bool|0,0|NULL|When wal_level set to minimal, parameters archive_mode can not be used.|
archive_timeout|int|0,1073741823|s|Forced to switch WAL segment exceeds the parameter setting time. Since forced to switch off prematurely archive remains intact archive the same length. Therefore, archive_timeout to occupy a very small value will result in a huge archive storage space, it is recommended archive_timeout set to 60 seconds.|
array_nulls|bool|0,0|NULL|NULL|
audit_copy_exec|int|0,1|NULL|NULL|
audit_data_format|string|0,0|NULL|NULL|
audit_database_process|int|0,1|NULL|NULL|
audit_directory|string|0,0|NULL|NULL|
audit_dml_state|int|0,1|NULL|NULL|
audit_dml_state_select|int|0,1|NULL|NULL|
audit_xid_info|int|0,1|NULL|NULL|
audit_enabled|bool|0,0|NULL|NULL|
audit_file_remain_threshold|int|100,1048576|NULL|NULL|
audit_file_remain_time|int|0,730|NULL|NULL|
audit_function_exec|int|0,1|NULL|NULL|
audit_grant_revoke|int|0,1|NULL|NULL|
audit_login_logout|int|0,7|NULL|NULL|
audit_resource_policy|bool|0,0|NULL|NULL|
audit_rotation_interval|int|1,35791394|min|NULL|
audit_rotation_size|int|1024,1048576|kB|NULL|
audit_space_limit|int|1024,1073741824|kB|NULL|
audit_system_object|int|0,268435455|NULL|NULL|
audit_user_locked|int|0,1|NULL|NULL|
audit_user_violation|int|0,1|NULL|NULL|
audit_set_parameter|int|0,1|NULL|NULL|
no_audit_client|string|0,0|NULL|NULL|
full_audit_users|string|0,0|NULL|NULL|
audit_system_function_exec|int|0,1|NULL|NULL|
authentication_timeout|int|1,600|s|NULL|
autoanalyze|bool|0,0|NULL|NULL|
autovacuum|bool|0,0|NULL|Even if this parameter is set to off, when a transaction ID wraparound imminent, the database will automatically start the cleanup process automatically.|
autovacuum_analyze_scale_factor|real|0,100|NULL|NULL|
autovacuum_analyze_threshold|int|0,2147483647|NULL|NULL|
autovacuum_freeze_max_age|int64|100000,576460752303423487|NULL|NULL|
autovacuum_max_workers|int|0,262143|NULL|NULL|
autovacuum_naptime|int|1,2147483|s|NULL|
autovacuum_vacuum_cost_delay|int|-1,100|ms|NULL|
autovacuum_vacuum_cost_limit|int|-1,10000|NULL|NULL|
autovacuum_vacuum_scale_factor|real|0,100|NULL|NULL|
autovacuum_vacuum_threshold|int|0,2147483647|NULL|NULL|
autovacuum_io_limits|int|-1,1073741823|NULL|NULL|
autovacuum_mode|enum|analyze,vacuum,mix,none|NULL|NULL|
handle_toast_in_autovac|enum|on,off|NULL|NULL|
autoanalyze_timeout|int|0,2147483|NULL|NULL|
available_zone|string|0,0|NULL|NULL|
backslash_quote|enum|safe_encoding,on,off,true,false,yes,no,1,0|NULL|NULL|
backtrace_min_messages|enum|debug,debug5,debug4,debug3,debug2,debug1,log,info,notice,warning,error,fatal,panic|NULL|It will increase the cost of the system, when print the function stack information frequently. Therefore, when analyzing the problem, avoid setting the value of backtrace_min_messages for fatal following levels.|
bbox_dump_count|int|1,20|NULL|NULL|
bbox_dump_path|string|0,0|NULL|NULL|
b_format_behavior_compat_options|string|0,0|NULL|NULL|
d_format_behavior_compat_options|string|0,0|NULL|NULL|
behavior_compat_options|string|0,0|NULL|NULL|
disable_keyword_options|string|0,0|NULL|NULL|
plsql_compile_check_options|string|0,0|NULL|NULL|
bgwriter_delay|int|10,10000|ms|NULL|
bgwriter_lru_maxpages|int|0,1000|NULL|NULL|
bgwriter_lru_multiplier|real|0,10|NULL|NULL|
block_encryption_mode|enum|aes-128-cbc,aes-192-cbc,aes-256-cbc,aes-128-cfb1,aes-192-cfb1,aes-256-cfb1,aes-128-cfb8,aes-192-cfb8,aes-256-cfb8,aes-128-cfb128,aes-192-cfb128,aes-256-cfb128,aes-128-ofb,aes-192-ofb,aes-256-ofb|NULL|NULL|
bulk_read_ring_size|int|256,2147483647|kB|NULL|
bulk_write_ring_size|int|16384,2147483647|kB|NULL|
bytea_output|enum|escape,hex|NULL|NULL|
cache_connection|bool|0,0|NULL|NULL|
candidate_buf_percent_target|real|0.1,0.85|NULL|NULL|
dirty_page_percent_max|real|0.1,1|NULL|NULL|
group_concat_max_len|int64|0,9223372036854775807|NULL|NULL
check_function_bodies|bool|0,0|NULL|NULL|
checkpoint_completion_target|real|0,1|NULL|NULL|
standby_force_recycle_ratio|real|0,1|NULL|NULL|
checkpoint_segments|int|1,2147483646|NULL|NULL|
checkpoint_timeout|int|30,3600|s|NULL|
checkpoint_warning|int|0,2147483647|s|NULL|
check_implicit_conversions|bool|0,0|NULL|NULL|
client_encoding|string|0,0|NULL|It is not recommended to set this parameter in postgresql.conf and it will not take effect even if it is set in postgresql.conf.|
client_min_messages|enum|debug,debug5,debug4,debug3,debug2,debug1,log,info,notice,warning,error,fatal,panic|NULL|When client_min_messages and log_min_messages take the same value, the value represented by the different levels.|
cn_send_buffer_size|int|8,128|kB|NULL|
comm_proxy_attr|string|0,0|NULL|NULL|
comm_control_port|int|0,65535|NULL|NULL|
comm_max_datanode|int|1,8192|NULL|NULL|
comm_max_receiver|int|1,50|NULL|NULL|
comm_max_stream|int|1,60000|NULL|NULL|
comm_quota_size|int|0,2048000|kB|NULL|
comm_sctp_port|int|0,65535|NULL|NULL|
comm_stat_mode|bool|0,0|NULL|When comm_stat_mode set to on, printing large amount of log, and it will add extra overhead, reduce database performance. Please Open it only when debugging.|
comm_usable_memory|int|102400,1073741823|kB|This parameter is required according to the specific configuration and deployment environment memory, over the General Assembly cause OOM, too small will reduce the performance of SCTP communication library.|
comm_memory_pool|int|102400,1073741823|kB|This parameter is the memory pool size for communication.|
comm_memory_pool_percent|int|0,100|NULL|NULL|
comm_sender_buffer_size|int|1,1024|NULL|NULL|
commit_delay|int|0,100000|NULL|When you set up a non-zero value after the transaction executed with the commit is not written WAL immediately, while still on the WAL buffer, wait WalWriter process written to disk with periodically. If the system load is high, at the delay time, other transaction maybe have been ready to commit. But if there is no transaction ready to commit, the delay is a waste of time.|
commit_siblings|int|0,1000|NULL|NULL|
config_file|string|0,0|NULL|NULL|
connection_alarm_rate|real|0,1|NULL|NULL|
constraint_exclusion|enum|partition,on,off,true,false,yes,no,1,0|NULL|NULL|
enable_union_all_subquery_orderby|bool|0,0|NULL|NULL|
enable_ignore_case_in_dquotes|bool|0,0|NULL|NULL|
enable_pltype_name_check|bool|0,0|NULL|NULL|
instr_unique_sql_track_type|enum|all,top|NULL|NULL|
transform_to_numeric_operators|bool|0,0|NULL|NULL|
convert_string_to_digit|bool|0,0|NULL|Please don't modify this parameter which will change the type conversion rule and may lead to unpredictable behavior!|
cost_param|int|0,2147483647|NULL|NULL|
cpu_collect_timer|int|1,2147483647|NULL|NULL|
cstore_buffers|int|16384,1073741823|kB|NULL|
current_schema|string|0,0|NULL|NULL|
cursor_tuple_fraction|real|0,1|NULL|NULL|
data_directory|string|0,0|NULL|NULL|
data_replicate_buffer_size|int|4096,1072693248|kB|NULL|
data_sync_retry|bool|0,0|NULL|NULL|
datestyle|string|0,0|NULL|NULL|
db4ai_snapshot_mode|string|0,0|NULL|NULL|
db4ai_snapshot_version_delimiter|string|0,0|NULL|NULL|
db4ai_snapshot_version_separator|string|0,0|NULL|NULL|
deadlock_timeout|int|1,2147483647|ms|NULL|
debug_assertions|bool|0,0|NULL|NULL|
debug_pretty_print|bool|0,0|NULL|NULL|
debug_print_parse|bool|0,0|NULL|Only when log level is log or above log, the debug information will be output. When parameter set to on, debugging information will be recorded in the server, but not output to the client. By setting client_min_messages and log_min_messages parameters can change the log level.|
debug_print_plan|bool|0,0|NULL|Only when log level is log or above log, the debug information will be output. When parameter set to on, debugging information will be recorded in the server, but not output to the client. By setting client_min_messages and log_min_messages parameters can change the log level.|
debug_print_rewritten|bool|0,0|NULL|Only when log level is log or above log, the debug information will be output. When parameter set to on, debugging information will be recorded in the server, but not output to the client. By setting client_min_messages and log_min_messages parameters can change the log level.|
default_statistics_target|int|-100,10000|NULL|NULL|
default_tablespace|string|0,0|NULL|NULL|
default_text_search_config|string|0,0|NULL|NULL|
default_transaction_deferrable|bool|0,0|NULL|NULL|
default_transaction_isolation|enum|serializable,repeatable read,read committed,read uncommitted|NULL|NULL|
default_transaction_read_only|bool|0,0|NULL|NULL|
default_with_oids|bool|0,0|NULL|NULL|
segment_test_param|string|0,0|NULL|NULL|
sql_use_spacelimit|int|-1,2147483647|kB|the max space limit query can used on single DN.|
dynamic_library_path|string|0,0|NULL|NULL|
effective_cache_size|int|1,2147483647|kB|This parameter has no effect on GaussDB Kernel allocated shared memory size, it does not use the kernel disk buffer, it is only used to estimate. The values are used to calculate the disk page, each page is usually 8192 bytes. Higher than the default value may result in the use of index scans, lower values may result in the selection order of scan.|
effective_io_concurrency|int|0,1000|NULL|NULL|
enable_access_server_directory|bool|0,0|NULL|NULL|
enable_alarm|bool|0,0|NULL|NULL|
enable_analyze_check|bool|0,0|NULL|NULL|
enable_bbox_dump|bool|0,0|NULL|NULL|
enable_ffic_log|bool|0,0|NULL|NULL|
enable_default_index_deduplication|bool|0,0|NULL|NULL|
enable_nonowner_remote_ddl|bool|0,0|NULL|NULL|
enable_bitmapscan|bool|0,0|NULL|NULL|
enable_ai_stats|bool|0,0|NULL|NULL|
multi_stats_type|enum|bayesnet,mcv,all|NULL|NULL|
instr_unique_sql_count|int|0,2147483647|NULL|NULL|
track_stmt_session_slot|int|0,2147483647|NULL|NULL|
track_stmt_details_size|int64|0,100000000|NULL|NULL|
track_stmt_stat_level|string|0,0|NULL|NULL|
enable_availablezone|bool|0,0|NULL|NULL|
enable_instr_cpu_timer|bool|0,0|NULL|NULL|
enable_instr_rt_percentile|bool|0,0|NULL|NULL|
enable_instr_track_wait|bool|0,0|NULL|NULL|
enable_broadcast|bool|0,0|NULL|NULL|
enable_cbm_tracking|bool|0,0|NULL|Turn on cbm tracking function.|
enable_candidate_buf_usage_count|bool|0,0|NULL|NULL|
enable_change_hjcost|bool|0,0|NULL|NULL|
enable_copy_server_files|bool|0,0|NULL|NULL|
enable_consider_usecount|bool|0,0|NULL|NULL|
enable_sonic_hashjoin|bool|0,0|NULL|NULL|
enable_sonic_hashagg|bool|0,0|NULL|NULL|
enable_sonic_optspill|bool|0,0|NULL|NULL|
enable_codegen|bool|0,0|NULL|NULL|
enable_codegen_print|bool|0,0|NULL|Enable dump for llvm function|
enable_delta_store|bool|0,0|NULL|NULL|
enable_default_cfunc_libpath|bool|0,0|NULL|NULL|
enable_defer_calculate_snapshot|bool|0,0|NULL|NULL|
enable_expr_fusion|bool|0,0|NULL|NULL|
codegen_cost_threshold|int|0,2147483647|NULL|Decided to use LLVM optimization or not|
codegen_strategy|enum|partial,pure|NULL|NULL|
enable_compress_spill|bool|0,0|NULL|NULL|
enable_constraint_optimization|bool|0,0|NULL|Information Constrained Optimization is only limited to the HDFS foreign table. When you execute a query which does not contain HDFS foreign table, the parameter is set to off.|
enable_csqual_pushdown|bool|0,0|NULL|NULL|
enable_data_replicate|bool|0,0|NULL|When this parameter is set on, replication_type must be 0.|
enable_wal_shipping_compression|bool|0,0|NULL|NULL|
enable_mix_replication|bool|0,0|NULL|NULL|
enable_instance_metric_persistent|bool|0,0|NULL|NULL|
enable_logical_io_statistics|bool|0,0|NULL|NULL|
instance_metric_retention_time|int|0,3650|day|NULL|
enable_compress_hll|bool|0,0|NULL|NULL|
enable_fast_numeric|bool|0,0|NULL|Enable numeric optimize.|
enable_force_vector_engine|bool|0,0|NULL|NULL|
enable_global_plancache|bool|0,0|NULL|NULL|
enable_cachedplan_mgr|bool|0,0|NULL|NULL|
enable_global_syscache|bool|0,0|NULL|NULL|
gpc_clean_timeout|int|300,86400|NULL|NULL|
enable_hashagg|bool|0,0|NULL|NULL|
enable_hashjoin|bool|0,0|NULL|NULL|
enable_heap_multi_insert_for_insert_select|bool|0,0|NULL|NULL|
enable_sortgroup_agg|bool|0,0|NULL|NULL|
enable_hdfs_predicate_pushdown|bool|0,0|NULL|NULL|
enable_hypo_index|bool|0,0|NULL|NULL|
enable_indexonlyscan|bool|0,0|NULL|NULL|
enable_indexscan|bool|0,0|NULL|NULL|
enable_inner_unique_opt|bool|0,0|NULL|NULL|
enable_kill_query|bool|0,0|NULL|NULL|
enable_material|bool|0,0|NULL|NULL|
enable_memory_limit|bool|0,0|NULL|NULL|
enable_memory_context_control|bool|0,0|NULL|NULL|
enable_memory_context_check_debug|bool|0,0|NULL|NULL|
enable_mergejoin|bool|0,0|NULL|NULL|
enable_nestloop|bool|0,0|NULL|NULL|
enable_index_nestloop|bool|0,0|NULL|NULL|
enable_online_ddl_waitlock|bool|0,0|NULL|It is not recommended to enable this parameter except for online expansion.|
enable_user_metric_persistent|bool|0,0|NULL|NULL|
enable_opfusion|bool|0,0|NULL|NULL|
enable_partition_opfusion|bool|0,0|NULL|NULL|
enable_partitionwise|bool|0,0|NULL|NULL|
enable_pbe_optimization|bool|0,0|NULL|NULL|
enable_prevent_job_task_startup|bool|0,0|NULL|It is not recommended to enable this parameter except for scaling out.|
enable_security_policy|bool|0,0|NULL|NULL|
use_elastic_search|bool|0,0|NULL|NULL|
elastic_search_ip_addr|string|0,0|NULL|NULL
enable_resource_track|bool|0,0|NULL|NULL|
enable_resource_record|bool|0,0|NULL|NULL|
enable_roach_standby_cluster|bool|0,0|NULL|NULL|
enable_save_datachanged_timestamp|bool|0,0|NULL|NULL|
enable_seqscan|bool|0,0|NULL|NULL|
enable_seqscan_dopcost|bool|0,0|NULL|NULL|
enable_show_any_tuples|bool|0,0|NULL|NULL|
enable_sort|bool|0,0|NULL|NULL|
enable_incremental_catchup|bool|0,0|NULL|NULL|
wait_dummy_time|int|1,2147483647|NULL|NULL|
heap_bulk_read_size|int|0,64|kB|Bulk blocks number for seqscan pre-read.|
vacuum_bulk_read_size|int|0,64|kB|Bulk blocks number for vacuum pre-read.|
cbm_xlog_files_epoch|int|1,16|NULL|NULL|
cbm_threads_num|int|1,10|NULL|NULL|
max_active_global_temporary_table|int|0,1000000|NULL|NULL|
max_inner_tool_connections|int|1,0x3FFFF|NULL|NULL|
max_recursive_times|int|0,2147483647|NULL|NULL|
enable_tidscan|bool|0,0|NULL|NULL|
enable_thread_pool|bool|0,0|NULL|NULL|
thread_pool_attr|string|0,0|NULL|NULL|
thread_pool_stream_attr|string|0,0|NULL|NULL|
resilience_threadpool_reject_cond|string|0,0|NULL|NULL|
track_stmt_retention_time|string|0,0|NULL|NULL|
track_stmt_standby_chain_size|string|0,0|NULL|NULL|
enable_vector_engine|bool|0,0|NULL|NULL|
enableseparationofduty|bool|0,0|NULL|NULL|
enable_nonsysadmin_execute_direct|bool|0,0|NULL|NULL|
operation_mode|bool|0,0|NULL|NULL|
enforce_a_behavior|bool|0,0|NULL|NULL|
escape_string_warning|bool|0,0|NULL|NULL|
event_source|string|0,0|NULL|NULL|
exit_on_error|bool|0,0|NULL|NULL|
explain_dna_file|string|0,0|NULL|NULL|
explain_perf_mode|enum|normal,pretty,summary,run|NULL|NULL|
resource_track_level|enum|none,query,operator|NULL|NULL|
external_pid_file|string|0,0|NULL|NULL|
extra_float_digits|int|-15,3|NULL|NULL|
failed_login_attempts|int|0,1000|NULL|NULL|
force_bitmapand|bool|0,0|NULL|NULL|
from_collapse_limit|int|1,2147483647|NULL|NULL|
fsync|bool|0,0|NULL|Using the fsync() system function can guarantee that when the operating system exception or hardware crash occurs, you can restore data to a consistent state. When fsync set to off, unable to restore the original data when the system crashes, it will cause the database unusable.|
full_page_writes|bool|0,0|NULL|When full_page_writes set to off, unable to restore the original data when the system crashes, it will cause the database unusable.|
float_suffix_acceptance|bool|0,0|NULL|NULL|
geqo|bool|0,0|NULL|Usually geqo do not set to off in the implementation process, geqo_threshold variable provides a more sophisticated method of control GEQO.|
geqo_effort|int|1,10|NULL|NULL|
geqo_generations|int|0,2147483647|NULL|NULL|
hadr_max_size_for_xlog_receiver|int|0,2147483647|kB|NULL|
hadr_recovery_time_target|int|0,3600|NULL|NULL|
standby_recycle_interval|int|0,86400|s|NULL|
standby_max_query_time|int|0,86400|s|NULL|
base_page_saved_interval|int|5,2000|NULL|NULL|
hadr_recovery_point_target|int|0,3600|NULL|NULL|
hadr_super_user_record_path|string|0,0|NULL|NULL|
hll_default_log2m|int|10,16|NULL|NULL|
hll_default_log2explicit|int|0,12|NULL|NULL|
hll_default_log2sparse|int|0,14|NULL|NULL|
hll_duplicate_check|int|0,1|NULL|NULL|
hll_default_regwidth|int|1,5|NULL|NULL|
hll_default_sparseon|int|0,1|NULL|NULL|
hll_max_sparse|int|-1,2147483647|NULL|NULL|
geqo_pool_size|int|0,2147483647|NULL|NULL|
geqo_seed|real|0,1|NULL|NULL|
geqo_selection_bias|real|1.5,2|NULL|NULL|
geqo_threshold|int|2,2147483647|NULL|NULL|
gin_fuzzy_search_limit|int|0,2147483647|NULL|NULL|
gs_clean_timeout|int|0,2147483|s|NULL|
hashagg_table_size|int|0,1073741823|NULL|NULL|
hba_file|string|0,0|NULL|NULL|
hot_standby|bool|0,0|NULL|When hot_standby set to on, wal_level must be set to hot_standby. Otherwise it will cause the database can not be started. In the dual-system environments, hot_standby can not be set to off.|
hot_standby_feedback|bool|0,0|NULL|NULL|
ident_file|string|0,0|NULL|NULL|
ignore_checksum_failure|bool|0,0|NULL|Continues processing after a checksum failure.|
ignore_system_indexes|bool|0,0|NULL|When ignore_system_indexes set to on, it is very useful for recovering data from the table which system index is corrupted.|
sql_ignore_strategy|string|0,0|NULL|NULL|
parctl_min_cost|int|-1,2147483647|NULL|NULL|
io_control_unit|int|1000,1000000|NULL|NULL|
io_limits|int|0,1073741823|NULL|NULL|
io_priority|enum|none,low,medium,high|NULL|NULL|
gin_pending_list_limit|int|64,2147483647|kB|NULL|
intervalstyle|enum|postgres,postgres_verbose,sql_standard,iso_8601,a|NULL|NULL|
join_collapse_limit|int|1,2147483647|NULL|NULL|
krb_caseins_users|bool|0,0|NULL|NULL|
krb_server_keyfile|string|0,0|NULL|NULL|
krb_srvname|string|0,0|NULL|NULL|
lc_messages|string|0,0|NULL|NULL|
lc_monetary|string|0,0|NULL|NULL|
lc_numeric|string|0,0|NULL|NULL|
lc_time|string|0,0|NULL|NULL|
listen_addresses|string|0,0|NULL|NULL|
lo_compat_privileges|bool|0,0|NULL|NULL|
local_bind_address|string|0,0|NULL|NULL|
local_preload_libraries|string|0,0|NULL|NULL|
lockwait_timeout|int|0,2147483647|ms|NULL|
log_autovacuum_min_duration|int|-1,2147483647|ms|NULL|
log_checkpoints|bool|0,0|NULL|NULL|
log_connections|bool|0,0|NULL|NULL|
log_destination|string|0,0|NULL|NULL|
log_directory|string|0,0|NULL|NULL|
query_log_directory|string|0,0|NULL|NULL|
asp_log_directory|string|0,0|NULL|NULL|
perf_directory|string|0,0|NULL|NULL|
query_log_file|string|0,0|NULL|NULL|
log_disconnections|bool|0,0|NULL|NULL|
log_duration|bool|0,0|NULL|NULL|
log_error_verbosity|enum|terse,default,verbose|NULL|NULL|
log_executor_stats|bool|0,0|NULL|NULL|
log_file_mode|int|0,511|NULL|NULL|
log_filename|string|0,0|NULL|NULL|
asp_flush_mode|string|0,0|NULL|NULL|
asp_log_filename|string|0,0|NULL|NULL|
log_hostname|bool|0,0|NULL|NULL|
log_line_prefix|string|0,0|NULL|NULL|
log_lock_waits|bool|0,0|NULL|NULL|
instr_rt_percentile_interval|int|0,3600|s|NULL|
wdr_snapshot_interval|int|10,60|min|NULL|
wdr_snapshot_retention_days|int|1,8|NULL|NULL|
wdr_snapshot_query_timeout|int|100,2147483647|s|NULL|
enable_wdr_snapshot|bool|0,0|NULL|NULL|
enable_set_variable_b_format|bool|0,0|NULL|NULL|
enable_binary_special_a_format|bool|0,0|NULL|NULL|
enable_asp|bool|0,0|NULL|NULL|
enable_startwith_debug|bool|0,0|NULL|NULL|
enable_stmt_track|bool|0,0|NULL|NULL|
track_stmt_parameter|bool|0,0|NULL|NULL|
asp_sample_num|int|10,100000|NULL|NULL|
asp_sample_interval|int|1,10|s|NULL|
asp_flush_rate|int|1,10|NULL|NULL|
asp_retention_days|int|1,7|NULL|NULL|
log_min_duration_statement|int|-1,2147483647|ms|NULL|
log_min_error_statement|enum|debug,debug5,debug4,debug3,debug2,debug1,log,info,notice,warning,error,fatal,panic|NULL|It recommended that it is set to error.|
log_min_messages|enum|debug,debug5,debug4,debug3,debug2,debug1,log,info,notice,warning,error,fatal,panic|NULL|When client_min_messages and log_min_messages take the same value, the value represented by the different levels. It recommended that it is set to warning.|
logging_module|string|0,0|NULL|NULL|
analysis_options|string|0,0|NULL|NULL|
log_parser_stats|bool|0,0|NULL|NULL|
log_planner_stats|bool|0,0|NULL|NULL|
log_rotation_age|int|0,35791394|min|NULL|
log_rotation_size|int|0,2097151|kB|NULL|
log_max_size|int|0,2147483647|kB|NULL|
log_statement|enum|none,ddl,mod,all|NULL|Even if log_statement set to all, simple statement contains a syntax error will not be recorded. Unless log_min_error_statement set to error or lower to records that contain simple syntax errors in the statement.|
log_statement_stats|bool|0,0|NULL|NULL|
log_temp_files|int|-1,2147483647|kB|NULL|
log_timezone|string|0,0|NULL|NULL|
log_truncate_on_rotation|bool|0,0|NULL|NULL|
logging_collector|bool|0,0|NULL|Logging_collector can be set to off when the server logs are sent to stderr. In this case the log messages are sent to stderr server to the space. The disadvantage of this method is difficult to do log rollback, applies only to a small log capacity.|
maintenance_work_mem|int|1024,2147483647|kB|NULL|
max_compile_functions|int|1,2147483647|NULL|NULL|
max_connections|int|10,262143|NULL|NULL|
max_files_per_process|int|25,2147483647|NULL|NULL|
max_io_capacity|int|30720,10485760|kB|NULL|
max_loaded_cudesc|int|100,1073741823|NULL|NULL|
max_locks_per_transaction|int|10,2147483647|NULL|NULL|
max_pred_locks_per_transaction|int|10,2147483647|NULL|NULL|
max_prepared_transactions|int|0,262143|NULL|NULL|
max_process_memory|int|2097152,2147483647|kB|NULL|
local_syscache_threshold|int|1024,524288|kB|NULL|
global_syscache_threshold|int|16384,1073741824|kB|NULL|
session_statistics_memory|int|5120,1073741823|kB|NULL|
session_history_memory|int|10240,1073741823|kB|NULL|
max_replication_slots|int|0,1024|NULL|NULL|
enable_slot_log|bool|0,0|NULL|NULL|
max_changes_in_memory|int|1,2147483647|NULL|NULL|
max_cached_tuplebufs|int|1,2147483647|NULL|NULL|
max_stack_depth|int|100,2147483647|kB|NULL|
max_standby_archive_delay|int|-1,2147483647|ms|'-1' means to permit backup machine waits until the query of conflict is completed.|
max_standby_streaming_delay|int|-1,2147483647|ms|NULL|
recovery_min_apply_delay|int|0,2147483647|ms|NULL|
max_user_defined_exception|int|1000,1000|NULL|NULL|
max_wal_senders|int|0,1024|NULL|Check whether the new value of max_wal_senders is less than max_connections and wal_level is archive, hot_standby or logical, otherwise the gaussdb will start failed.|
max_redo_log_size|int|163840,2147483647|kB|NULL|
memorypool_enable|bool|0,0|NULL|NULL|
memory_fault_percent|int|0,2147483647|NULL|NULL|
memorypool_size|int|131072,1073741823|kB|NULL|
memory_detail_tracking|string|0,0|NULL|This parameter memory_detail_tracking only can be used in debug version. If it is set in the release version, it will lead the cluster does not work properly. Therefore, before making the settings, please make sure that the cluster version is debug.|
memory_tracking_mode|enum|none,normal,executor,fullexec|NULL|NULL|
memory_trace_level|enum|none,level1,level2|NULL|NULL|
resilience_memory_reject_percent|string|0,0|NULL|NULL|
modify_initial_password|bool|0,0|NULL|NULL|
most_available_sync|bool|0,0|NULL|NULL|
ngram_gram_size|int|1,4|NULL|NULL|
ngram_punctuation_ignore|bool|0,0|NULL|NULL|
ngram_grapsymbol_ignore|bool|0,0|NULL|NULL|
nls_timestamp_format|string|0,0|NULL|NULL|
nls_length_semantics|string|0,0|NULL|NULL|
omit_encoding_error|bool|0,0|NULL|NULL|
mot_config_file|string|0,0|NULL|MOT Configuration file name.|
opfusion_debug_mode|enum|off,log|NULL|NULL|
partition_lock_upgrade_timeout|int|-1,3000|NULL|NULL|
partition_max_cache_size|int|4096,1073741823|kB|NULL|
partition_mem_batch|int|1,65535|NULL|NULL|
password_effect_time|real|0,999|NULL|NULL|
password_encryption_type|int|0,3|NULL|NULL|
password_lock_time|real|0,365|d|password_lock_time and failed_login_attempts must have positive for lock and unlock functions to work as.|
password_max_length|int|6,999|NULL|NULL|
password_min_digital|int|0,999|NULL|NULL|
password_min_length|int|6,999|NULL|NULL|
password_min_lowercase|int|0,999|NULL|NULL|
password_min_special|int|0,999|NULL|NULL|
password_min_uppercase|int|0,999|NULL|NULL|
password_notify_time|int|0,999|NULL|NULL|
password_policy|int|0,1|NULL|NULL|
password_reuse_max|int|0,1000|NULL|NULL|
password_reuse_time|real|0,3650|NULL|Checks the configuration parameters password_reuse_time and password_reuse_max when modifying password, as long as either one, can be considered the password can be reused.|
pgxc_node_name|string|0,0|NULL|NULL|
plan_mode_seed|int|-1,2147483647|NULL|NULL|
pooler_timeout|int|0,7200|s|NULL|
pooler_connect_timeout|int|0,7200|s|NULL|
pooler_cancel_timeout|int|0,7200|s|NULL|
port|int|1,65535|NULL|NULL|
post_auth_delay|int|0,2147|s|NULL|
pre_auth_delay|int|0,60|s|NULL|
primary_slotname|string|0,0|NULL|NULL|
psort_work_mem|int|64,2147483647|kB|Several running sessions may be storing in the action column to sort the table at the same time. Therefore, the total memory usage may be several times than psort_work_mem.|
query_band|string|0,0|NULL|NULL|
query_dop|int|1,64|NULL|NULL|
gms_sql_max_open_cursor_count|int|10,500|NULL|NULL|
query_mem|int|0,2147483647|kB|Sets the memory to be reserved for a statement.|
query_max_mem|int|0,2147483647|kB|Sets the max memory to be reserved for a statement.|
quote_all_identifiers|bool|0,0|NULL|NULL|
raise_errors_if_no_files|bool|0,0|NULL|NULL|
remotetype|enum|application,coordinator,datanode,gtm,gtmproxy,internaltool,gtmtool|NULL|NULL|
replconninfo1|string|0,0|NULL|NULL|
replconninfo2|string|0,0|NULL|NULL|
replconninfo3|string|0,0|NULL|NULL|
replconninfo4|string|0,0|NULL|NULL|
replconninfo5|string|0,0|NULL|NULL|
replconninfo6|string|0,0|NULL|NULL|
replconninfo7|string|0,0|NULL|NULL|
replconninfo8|string|0,0|NULL|NULL|
cross_cluster_replconninfo1|string|0,0|NULL|NULL|
cross_cluster_replconninfo2|string|0,0|NULL|NULL|
cross_cluster_replconninfo3|string|0,0|NULL|NULL|
cross_cluster_replconninfo4|string|0,0|NULL|NULL|
cross_cluster_replconninfo5|string|0,0|NULL|NULL|
cross_cluster_replconninfo6|string|0,0|NULL|NULL|
cross_cluster_replconninfo7|string|0,0|NULL|NULL|
cross_cluster_replconninfo8|string|0,0|NULL|NULL|
replication_type|int|0,2|NULL|When this parameter is set to 1(multi_standy), enable_data_replicate must be off. It can not be changed once the cluster is installed.|
repl_auth_mode|enum|default,off,uuid|NULL|NULL|
repl_uuid|string|0,0|NULL|Set uuid value used for replication authentification. Accecpt alphanumeric character, case insensitive.|
ha_module_debug|bool|0,0|NULL|NULL|
require_ssl|bool|0,0|NULL|NULL|
resource_track_log|enum|summary,detail|NULL|NULL|
restart_after_crash|bool|0,0|NULL|NULL|
restrict_nonsystem_relation_kind|string|0,0|NULL|NULL|
rewrite_rule|enum|lazyagg,magicset,partialpush,uniquecheck,disablerep,intargetlist,predpush,predpushforce,predpushnormal,disable_pullup_expr_sublink,enable_sublink_pullup_enhanced,none|NULL|NULL|
safe_data_path|string|0,0|NULL|NULL|
search_path|string|0,0|NULL|NULL|
session_replication_role|enum|origin,replica,local|NULL|When this parameter is set, any cached query plan will be lost before.|
session_timeout|int|0,86400|s|GaussDB Kernel gsql client has an automatic reconnection mechanism, when the timeout, the gsql will be reconnection after disconnection.|
idle_in_transaction_session_timeout|int|0,86400|s|Sets the maximum allowed idle time between queries, when in a transaction.|
shared_buffers|int|16,1073741823|kB|NULL|
huge_page_size|int|0,1073741823|kB|NULL|
pca_shared_buffers|int|8,1073741823|kB|NULL|
shared_preload_libraries|string|0,0|NULL|NULL|
show_acce_estimate_detail|bool|0,0|NULL|NULL|
show_fdw_remote_plan|bool|0,0|NULL|NULL|
skew_option|enum|normal,lazy,off|NULL|NULL|
sql_inheritance|bool|0,0|NULL|NULL|
ssl|bool|0,0|NULL|NULL|
ssl_ca_file|string|0,0|NULL|NULL|
ssl_cert_file|string|0,0|NULL|NULL|
ssl_ciphers|string|0,0|NULL|NULL|
ssl_crl_file|string|0,0|NULL|NULL|
ssl_key_file|string|0,0|NULL|NULL|
ssl_use_tlcp|bool|0,0|NULL|NULL|
ssl_enc_cert_file|string|0,0|NULL|NULL|
ssl_enc_key_file|string|0,0|NULL|NULL|
ssl_renegotiation_limit|int|0,2147483647|kB|NULL|
ssl_cert_notify_time|int|7,180|d|Alarm days before ssl cert expires.|
standard_conforming_strings|bool|0,0|NULL|NULL|
standby_shared_buffers_fraction|real|0.1,1|NULL|NULL|
statement_timeout|int|0,2147483647|ms|NULL|
stats_temp_directory|string|0,0|NULL|NULL|
num_internal_lock_partitions|string|0,0|NULL|NULL|
num_slru_buffers|string|0,0|NULL|NULL|
string_hash_compatible|bool|0,0|NULL|NULL|
enable_slow_query_log|bool|0,0|NULL|NULL|
support_batch_bind|bool|0,0|NULL|NULL|
enable_beta_opfusion|bool|0,0|NULL|NULL|
support_extended_features|bool|0,0|NULL|NULL|
lastval_supported|bool|0,0|NULL|NULL|
enable_beta_features|bool|0,0|NULL|NULL|
b_compatibility_user_host_auth|bool|0,0|NULL|NULL|
synchronize_seqscans|bool|0,0|NULL|NULL|
synchronous_commit|enum|local,remote_receive,remote_write,remote_apply,on,off,true,false,yes,no,2,1,0|NULL|NULL|
synchronous_standby_names|string|0,0|NULL|NULL|
sysadmin_reserved_connections|int|0,262143|NULL|sysadmin_reserved_connections must be less than max_connections|
syslog_facility|enum|local0,local1,local2,local3,local4,local5,local6,local7|NULL|NULL|
syslog_ident|string|0,0|NULL|NULL|
table_skewness_warning_rows|int|0,2147483647|NULL|NULL|
table_skewness_warning_threshold|real|0,1|NULL|NULL|
tcp_keepalives_count|int|0,100|NULL|NULL|
tcp_keepalives_idle|int|0,3600|s|If the operating system does not support TCP_KEEPIDLE option, the value of this parameter must be zero. On the operating system via a Unix domain socket connection, this parameter will be ignored.|
tcp_keepalives_interval|int|0,180|s|If the operating system does not support TCP_KEEPIDLE option, the value of this parameter must be 1. On the operating system via a Unix domain socket connection, this parameter will be ignored.|
tcp_user_timeout|int|0,3600000|ms|If the operating system does not support TCP_USER_TIMEOUT option, the value of this parameter will be ignored. On the operating system via a Unix domain socket connection, this parameter will be ignored.|
temp_buffers|int|100,1073741823|kB|NULL|
temp_file_limit|int|-1,2147483647|kB|SQL query using a temporary table space when executed unless the system.|
temp_tablespaces|string|0,0|NULL|NULL|
timezone|string|0,0|NULL|NULL|
timezone_abbreviations|string|0,0|NULL|NULL|
trace_notify|bool|0,0|NULL|NULL|
trace_recovery_messages|enum|debug,debug5,debug4,debug3,debug2,debug1,log,info,notice,warning,error,fatal,panic|NULL|NULL|
trace_sort|bool|0,0|NULL|NULL|
track_activities|bool|0,0|NULL|NULL|
track_activity_query_size|int|100,102400|NULL|NULL|
track_counts|bool|0,0|NULL|NULL|
track_functions|enum|none,pl,all|NULL|When the SQL function to be setted 'inline' function for querying. Regardless of whether this option is setted. The SQL function can not be traced.|
track_io_timing|bool|0,0|NULL|NULL|
track_thread_wait_status_interval|int|0,1440|min|NULL|
track_sql_count|bool|0,0|NULL|NULL|
transaction_deferrable|bool|0,0|NULL|NULL|
transaction_isolation|string|0,0|NULL|NULL|
transaction_pending_time|int|-1,1073741823|NULL|NULL|
transaction_read_only|bool|0,0|NULL|NULL|
transparent_encrypted_string|string|0,0|NULL|NULL|
transparent_encrypt_kms_url|string|0,0|NULL|NULL|
transparent_encrypt_kms_region|string|0,0|NULL|NULL|
enable_tde|bool|0,0|NULL|NULL|
tde_cmk_id|string|0,0|NULL|NULL|
try_vector_engine_strategy|enum|off,force,optimal|NULL|NULL
plog_merge_age|int|0,2147483647|ms|how long to aggregate profile logs.0 disable logging. suggest setting value is 1000 times.|
fault_mon_timeout|int|0,1440|min|how many miniutes to monitor lwlock. 0 will disable that.|
transform_null_equals|bool|0,0|NULL|This only affects 'expr=NULL', not other comparison operators or other expressions involving some of the equality operator computing (such as IN).|
ansi_nulls|bool|0,0|NULL|Contrary to the transform_null_equals parameter.|
udf_memory_limit|int|204800,2147483647|kB|NULL|
uncontrolled_memory_context|string|0,0|NULL|NULL|
unix_socket_directory|string|0,0|NULL|NULL|
unix_socket_group|string|0,0|NULL|NULL|
unix_socket_permissions|int|0,511|NULL|NULL|
update_lockwait_timeout|int|0,2147483647|ms|NULL|
uppercase_attribute_name|bool|0,0|NULL|NULL|
use_workload_manager|bool|0,0|NULL|NULL|
user_metric_retention_time|int|0,3650|day|NULL|
ustore_attr|string|0,0|NULL|NULL|
enable_ustore|bool|0,0|NULL|Enable to create ustore table|
enable_default_ustore_table|bool|0,0|NULL|Enable to create ustore table by default|
enable_default_pcr_index|bool|0,0|NULL|Enable to create pcr index by default|
enable_gtt_concurrent_truncate|bool|0,0|NULL|Enable concurrent truncate table for GTT|
foreign_key_checks|bool|0,0|NULL|Enable foreign key check on insert, update or drop operation,only applicable to b-format db.|
unique_checks|bool|0,0|NULL|Enable unique check,only applicable to b-format db.|
reserve_space_for_nullable_atts|bool|0,0|NULL|Enable reserve space for nullable attributes, only applicable to ustore|
undo_space_limit_size|int|819200,17179869184|kB|Maximum physical space of the undo command|
undo_limit_size_per_transaction|int|2048,17179869184|kB|Maximum space for allocating undo resources in a transaction|
max_undo_workers|int|1,100|NULL|Maximum possible active async rollback workers at a time|
vacuum_cost_delay|int|0,100|ms|NULL|
vacuum_cost_limit|int|1,10000|NULL|NULL|
vacuum_cost_page_dirty|int|0,10000|NULL|NULL|
vacuum_cost_page_hit|int|0,10000|NULL|NULL|
vacuum_cost_page_miss|int|0,10000|NULL|NULL|
vacuum_gtt_defer_check_age|int|0,1000000|NULL|NULL|
undo_retention_time|int|0,259200|s|Sets the maximum retention time of undo|
version_retention_age|int64|0,576460752303423487|NULL|NULL|
enable_recyclebin|bool|0,0|NULL|Enable recyclebin for user-defined objects restore|
recyclebin_retention_time|int|1,2147483647|s|Sets the maximum retention time of objects in recyclebin|
enable_tpc_fragment_chunks|bool|0,0|NULL|Enable tpc fragment chunks to ensure that chunks are continuously arranged when updating transparent page compression table|
vacuum_defer_cleanup_age|int64|0,1000000|NULL|NULL|
vacuum_freeze_min_age|int64|0,576460752303423487|NULL|NULL|
vacuum_freeze_table_age|int64|0,576460752303423487|NULL|NULL|
hll_default_expthresh|int64|-1,7|NULL|NULL|
wal_buffers|int|-1,262144|kB|Every time a transaction is committed, the contents of WAL buffers are written to disk, it is set to a large value will not bring significant performance gains. If you set it to hundreds of megabytes, you may have written to the disk to improve performance on the server a lot of real-time transaction commits. According to experience, the default value is sufficient for most situations.|
wal_keep_segments|int|2,2147483647|NULL| When the server is turned on or archive log recovery from the checkpoint, the number of reserved log files may be larger than the set value wal_keep_segments. If this parameter is set too low, at the time of the transaction log backup requests, the new transaction log may have been produced coverage request fails, disconnect the master and slave relationship.|
wal_level|enum|minimal,archive,hot_standby,logical|NULL|If you need to copy the data stream for WAL log archiving and standby machine. You must be set to the parameter with archive or hot_standby. If this parameter is setted to archive. The hot_standby must be setted to off, otherwise it will cause the database can not be started, at the same time the max_wal_senders must be set at least 1.|
wal_log_hints|bool|0,0|NULL|Writes full pages to WAL when first modified after a checkpoint, even for a non-critical modifications.|
wal_receiver_buffer_size|int|4096,1047552|kB|NULL|
wal_receiver_status_interval|int|0,2147483|s|NULL|
wal_receiver_timeout|int|0,2147483647|ms|NULL|
wal_receiver_connect_timeout|int|0,2147483|s|NULL|
wal_receiver_connect_retries|int|1,2147483647|NULL|NULL|
wal_sender_timeout|int|0,2147483647|ms|If the host larger data rebuild operation requires increasing the value of this parameter,the host data at 500G, refer to this parameter is 600. This value can not be greater than the wal_receiver_timeout or database rebuilding timeout parameter.|
wal_sync_method|enum|fsync,fsync_writethrough,fdatasync,open_sync,open_datasync|NULL|If fsync set to off, this parameter setting does not make sense, because all data updates are not forced to be written to disk.|
wal_writer_delay|int|1,10000|ms|If the time is too long will cause WAL buffers memory shortage, time is too short will cause WAL continue to write, increase disk I/O burden.|
wal_flush_timeout|int|0,90000000|NULL|set timeout when iterator table entry.|
wal_flush_delay|int|0,90000000|NULL|set delay time when iterator table entry.|
walsender_max_send_size|int|8,2147483647|kB|NULL|
basebackup_timeout|int|0,2147483647|s|NULL|
work_mem|int|64,2147483647|kB|For complex queries, it may run several concurrent sort or hash operation, each of which can use the amount of memory that this parameter is declared using the temporary file is insufficient. Also, several running sessions could be sorted the same time. Therefore, the total memory usage may be work_mem several times.|
xloginsert_locks|int|1,1000|NULL|NULL|
xmlbinary|enum|base64,hex|NULL|NULL|
xmloption|enum|content,document|NULL|NULL|
zero_damaged_pages|bool|0,0|NULL|NULL|
enable_bloom_filter|bool|0,0|NULL|NULL|
cstore_insert_mode|enum|auto,main,delta|NULL|NULL|
plan_cache_mode|enum|auto,force_generic_plan,force_custom_plan|NULL|NULL|
plan_cache_type_validation|bool|0,0|NULL|NULL|
remote_read_mode|enum|off,non_authentication,authentication|NULL|NULL|
enable_debug_vacuum|bool|0,0|NULL|NULL|
enable_vacuum_extreme_xmin|bool|0,0|NULL|Use extreme xmin to vacuum.|
enable_early_free|bool|0,0|NULL|NULL|
resource_track_cost|int|-1,2147483647|NULL|NULL|
resource_track_duration|int|0,2147483647|s|NULL|
topsql_retention_time|int|0,3650|NULL|NULL|
unique_sql_retention_time|int|1,3650|NULL|NULL|
backwrite_quantity|int|128,131072|kB|NULL|
cstore_backwrite_max_threshold|int|4096,1073741823|kB|NULL|
cstore_backwrite_quantity|int|1024,1048576|kB|NULL|
cstore_prefetch_quantity|int|1024,1048576|kB|NULL|
enable_adio_debug|bool|0,0|NULL|NULL|
enable_adio_function|bool|0,0|NULL|NULL|
enable_fast_allocate|bool|0,0|NULL|NULL|
enable_stream_replication|bool|0,0|NULL|NULL|
fast_extend_file_size|int|1024,1048576|kB|NULL|
prefetch_quantity|int|128,131072|kB|NULL|
enable_global_stats|bool|0,0|NULL|NULL|
td_compatible_truncation|bool|0,0|NULL|NULL|
enable_valuepartition_pruning|bool|0,0|NULL|NULL|
checkpoint_flush_after|int|0,256|kB|NULL|
bgwriter_flush_after|int|0,256|kB|NULL|
backend_flush_after|int|0,256|kB|NULL|
checkpoint_wait_timeout|int|2,3600|s|NULL|
sql_use_spacelimit|int|-1,2147483647|kB|the max space limit query can used on single DN.|
enable_hadoop_env|bool|0,0|NULL|NULL|
enable_upgrade_merge_lock_mode|bool|0,0|NULL|NULL|
job_queue_processes|int|0,1000|NULL|NULL|
enable_absolute_tablespace|bool|0,0|NULL|NULL|
enable_orc_cache|bool|0,0|NULL|NULL|
acceleration_with_compute_pool|bool|0,0|NULL|NULL|
acce_min_datasize_per_thread|int|0,2147483647|NULL|NULL|
max_resource_package|int|0,134217728|NULL|NULL|
qrw_inlist2join_optmode|string|0,0|NULL|GUC to control optimizer behavior of inlist2join query rewrite.|
auth_iteration_count|int|2048,134217728|NULL|NULL|
fencedudfmemorylimit|int|0,2147483647|kB|Sets the maximum memory to be used for fenced UDF by user.This parameter depends on UDFWorkerMemHardLimit parameter.|
udfworkermemhardlimit|int|0,2147483647|kB|Sets the hard memory limit to be used for fenced UDF.|
pljava_vmoptions|string|0,0|NULL|VMOptions to start the JVM in pljava when it is created.|
enable_extrapolation_stats|bool|0,0|NULL|NULL|
retry_ecode_list|string|0,0|NULL|NULL|
enable_dcf|bool|0,0|NULL|NULL|
dcf_ssl|bool|0,0|NULL|NULL|
dcf_node_id|int|1,2147483647|NULL|NULL|
dcf_config|string|0,0|NULL|GUC to control paxos cluster|
dcf_max_workers|int|10,262143|NULL|NULL|
dcf_data_path|string|0,0|NULL|NULL|
dcf_log_path|string|0,0|NULL|NULL|
dcf_log_level|string|0,0|NULL|NULL|
dcf_majority_groups|string|0,0|NULL|Sets majority groups for DCF, each group value must be contained dcf_config.|
dcf_election_timeout|int|1,600|s|NULL|
dcf_enable_auto_election_priority|int|0,1|NULL|NULL|
dcf_election_switch_threshold|int|0,2147483647|NULL|NULL|
dcf_run_mode|enum|0,2|NULL|NULL|
dcf_max_log_file_size|int|1,1000|MB|NULL|
dcf_log_backup_file_count|int|1,100|NULL|NULL|
dcf_log_file_permission|enum|600,640|NULL|NULL|
dcf_log_path_permission|enum|700,750|NULL|NULL|
dcf_mec_agent_thread_num|int|1,1000|NULL|NULL|
dcf_mec_reactor_thread_num|int|1,100|NULL|NULL|
dcf_mec_channel_num|int|1,64|NULL|NULL|
dcf_mem_pool_init_size|int|32,2147483647|MB|NULL|
dcf_mem_pool_max_size|int|32,2147483647|MB|NULL|
dcf_compress_algorithm|int|0,2|NULL|NULL|
dcf_compress_level|int|1,22|NULL|NULL|
dcf_socket_timeout|int|10,600000|ms|NULL|
dcf_connect_timeout|int|10,600000|ms|NULL|
dcf_rep_append_thread_num|int|1,1000|NULL|NULL|
dcf_mec_fragment_size|int|32,10240|NULL|NULL|
dcf_stg_pool_init_size|int|32,2147483647|MB|NULL|
dcf_stg_pool_max_size|int|32,2147483647|MB|NULL|
dcf_mec_pool_max_size|int|32,2147483647|MB|NULL|
dcf_mec_batch_size|int|0,1024|NULL|NULL|
dcf_flow_control_cpu_threshold|int|0,2147483647|NULL|NULL|
dcf_flow_control_net_queue_message_num_threshold|int|0,2147483647|NULL|NULL|
dcf_flow_control_disk_rawait_threshold|int|0,2147483647|NULL|NULL|
dcf_truncate_threshold|int|1,2147483647|NULL|NULL|
recovery_max_workers|int|0,20|NULL|NULL|
recovery_parse_workers|int|1,16|NULL|NULL|
recovery_redo_workers|int|1,8|NULL|NULL|
recovery_time_target|int|0,3600|NULL|NULL|
pagewriter_sleep|int|0,3600000|ms|NULL|
pagewriter_thread_num|int|1,16|NULL|NULL|
audit_thread_num|int|1,48|NULL|NULL|
dw_file_num|int|1,16|NULL|NULL|
dw_file_size|int|32,256|NULL|NULL|
parallel_recovery_batch|int|1,100000|NULL|NULL|
parallel_recovery_timeout|int|1,1000|ms|NULL|
parallel_recovery_dispatch_algorithm|int|1,2|NULL|NULL|
incremental_checkpoint_timeout|int|1,3600|s|NULL|
enable_incremental_checkpoint|bool|0,0|NULL|NULL|
enable_double_write|bool|0,0|NULL|NULL|
log_pagewriter|bool|0,0|NULL|NULL|
enable_xlog_insert_record_group|bool|0,0|NULL|NULL|
enable_xlog_prune|bool|0,0|NULL|NULL|
max_size_for_xlog_prune|int|0,2147483647|kB|NULL|
max_size_xlog_force_prune|int|0,2147483647|kB|NULL|
enable_page_lsn_check|bool|0,0|NULL|NULL|
upgrade_mode|int|0,2147483647|NULL|NULL|
advance_xlog_file_num|int|0,1000000|NULL|NULL|
numa_distribute_mode|string|0,0|NULL|NULL|
defer_csn_cleanup_time|int|0,2147483647|ms|NULL|
exrto_standby_read_opt|bool|0,0|NULL|NULL|
force_promote|int|0,1|NULL|NULL|
max_keep_log_seg|int|0,2147483647|NULL|NULL|
datanode_heartbeat_interval|int|1000,60000|ms|The value is best configured less than half of the wal_receiver_timeout and wal_sender_timeout.|
enable_auto_explain|bool|0,0|NULL|NULL|
auto_explain_level|enum|off,log,notice|NULL|NULL|
cost_weight_index|real|1e-10,1e+10|NULL|NULL|
default_limit_rows|real|-100,1.79769e+308|NULL|NULL|
sql_beta_feature|enum|partition_fdw_on,partition_opfusion,index_cost_with_leaf_pages_only,canonical_pathkey,join_sel_with_cast_func,no_unique_index_first,sel_semi_poisson,sel_expr_instr,param_path_gen,rand_cost_opt,param_path_opt,page_est_opt,a_style_coerce,predpush_same_level,enable_plsql_smp,disable_bitmap_cost_with_lossy_pages,none|NULL|NULL|
partition_iterator_elimination|bool|0,0|NULL|NULL|
enable_functional_dependency|bool|0,0|NULL|NULL|
enable_seqscan_fusion|bool|0,0|NULL|NULL|
enable_iud_fusion|bool|0,0|NULL|NULL|
enable_indexscan_optimization|bool|0,0|NULL|NULL|
max_logical_replication_workers|int|0,262143|NULL|Maximum number of logical replication worker processes.|
max_sync_workers_per_subscription|int|0,262143|NULL|Maximum number of table synchronization workers per subscription.|
walwriter_sleep_threshold|int64|1,50000|NULL|NULL|
walwriter_cpu_bind|int|-1,2147483647|NULL|NULL|
wal_file_init_num|int|0,1000000|NULL|NULL|
wal_file_preinit_threshold|int|1,100|NULL|Threshold for pre-initializing xlogs, in percentages.|
catchup2normal_wait_time|int|-1,10000|ms|The maximal allowed duration for waiting from catchup to normal state.|
max_concurrent_autonomous_transactions|int|0,1024|NULL|NULL|
sync_config_strategy|enum|all_node,only_sync_node,none_node|NULL|Synchronization strategy for configuration files between host and standby.|
time_to_target_rpo|int|0,3600|NULL|NULL|
disable_memory_protect|bool|0,0|NULL|NULL|
segment_buffers|int|16,1073741823|kB|NULL|
undo_zone_count|int|0,1048576|NULL|NULL|
stream_cluster_run_mode|enum|cluster_primary,cluster_standby|NULL|NULL|
xlog_file_size|int64|1048576,576460752303423487|B|The value must be an integer multiple of 16777216(16M)|
xlog_file_path|string|0,0|NULL|NULL|
max_standby_base_page_size|int64|1048576,562949953421311|kB|NULL|
max_standby_lsn_info_size|int64|1048576,562949953421311|kB|NULL|
plsql_show_all_error|bool|0,0|NULL|NULL|
partition_page_estimation|bool|0,0|NULL|NULL|
enable_auto_clean_unique_sql|bool|0,0|NULL|NULL|
pldebugger_timeout|int|1,86400|s|NULL|
xlog_lock_file_path|string|0,0|NULL|NULL|
keep_sync_window|int|0,2147483647|s|NULL|
redo_bind_cpu_attr|string|0,0|NULL|NULL|
logical_decode_options_default|string|0,0|NULL|NULL|
logical_sender_timeout|int|0,2147483647|ms|NULL|
var_eq_const_selectivity|bool|0,0|NULL|NULL|
enable_save_confirmed_lsn|bool|0,0|NULL|NULL|
ss_enable_dss|bool|0,0|NULL|NULL|
enable_segment|bool|0,0|NULL|NULL|
ss_dss_data_vg_name|string|0,0|NULL|NULL|
ss_dss_xlog_vg_name|string|0,0|NULL|NULL|
ss_dss_conn_path|string|0,0|NULL|NULL|
ss_enable_dms|bool|0,0|NULL|NULL|
ss_enable_catalog_centralized|bool|0,0|NULL|NULL|
ss_enable_dynamic_trace|bool|0,0|NULL|NULL|
ss_enable_reform_trace|bool|0,0|NULL|NULL|
ss_enable_ssl|bool|0,0|NULL|NULL|
ss_enable_aio|bool|0,0|NULL|NULL|
ss_enable_ondemand_realtime_build|bool|0,0|NULL|NULL|
ss_enable_ondemand_recovery|bool|0,0|NULL|NULL|
ss_interconnect_channel_count|int|1,32|NULL|NULL|
ss_work_thread_count|int|16,128|NULL|NULL|
ss_work_thread_pool_attr|string|0,0|NULL|NULL|
ss_fi_packet_loss_prob|int|0,100|NULL|NULL|
ss_fi_net_latency_ms|int|0,4294967295|NULL|NULL|
ss_fi_cpu_latency_ms|int|0,4294967295|NULL|NULL|
ss_fi_process_fault_prob|int|0,100|NULL|NULL|
ss_fi_custom_fault_param|int|0,4294967295|NULL|NULL|
ss_fi_packet_loss_entries|string|0,0|NULL|NULL|
ss_fi_net_latency_entries|string|0,0|NULL|NULL|
ss_fi_cpu_latency_entries|string|0,0|NULL|NULL|
ss_fi_process_fault_entries|string|0,0|NULL|NULL|
ss_fi_custom_fault_entries|string|0,0|NULL|NULL|
ss_ondemand_recovery_mem_size|int|1048576,104857600|kB|NULL|
ss_recv_msg_pool_size|int|32768,1048576|kB|NULL|
ss_interconnect_type|string|0,0|NULL|NULL|
ss_log_level|int|0,887|NULL|NULL|
ss_log_backup_file_count|int|0,1024|NULL|NULL|
ss_log_max_file_size|int|1024,4194304|kB|NULL|
ss_parallel_thread_count|int|0,64|NULL|NULL|
ss_instance_id|int|0,63|NULL|NULL|
ss_interconnect_url|string|0,0|NULL|NULL|
ss_rdma_work_config|string|0,0|NULL|NULL|
ss_ock_log_path|string|0,0|NULL|NULL|
ss_enable_scrlock|bool|0,0|NULL|NULL|
ss_enable_scrlock_sleep_mode|bool|0,0|NULL|NULL|
ss_scrlock_server_port|int|1024,65535|NULL|NULL|
ss_scrlock_worker_count|int|2,16|NULL|NULL|
ss_scrlock_worker_bind_core|string|0,0|NULL|NULL|
ss_scrlock_server_bind_core|string|0,0|NULL|NULL|
dolphin_server_port|int|1024,65535|NULL|NULL|
enable_dolphin_proto|bool|0,0|NULL|NULL|
dolphin_hot_standby|bool|0,0,|NULL|NULL|
enable_remote_execute|bool|0,0|NULL|NULL|
light_comm|bool|0,0|NULL|NULL|
enable_proc_coverage|bool|0,0|NULL|NULL|
ignore_standby_lsn_window|int|0,2147483647|ms|NULL|
ignore_feedback_xmin_window|int|0,2147483647|ms|NULL|
ss_enable_bcast_getoldestxmin|bool|0,0|NULL|NULL|
ss_enable_bcast_snapshot|bool|0,0|NULL|NULL|
ss_txnstatus_cache_size|int|0,524288|NULL|NULL|
subscription_conflict_resolution|enum|error,apply_remote,keep_local|NULL|NULL|
time_record_level|int|0,10|NULL|NULL|
enable_record_nettime|bool|0,0|NULL|NULL|
enable_mot_server|bool|0,0|NULL|NULL|
ss_disaster_mode|enum|single,stream,dorado|NULL|NULL|
enable_uwal|bool|0,0|NULL|NULL|
uwal_config|string|0,0|NULL|GUC to control paxos cluster|
uwal_disk_size|int64|8589934592,4398046511104|NULL|NULL|
uwal_devices_path|string|0,0|NULL|NULL|
uwal_log_path|string|0,0|NULL|NULL|
uwal_rpc_compression_switch|bool|0,0|NULL|NULL|
uwal_rpc_flowcontrol_switch|bool|0,0|NULL|NULL|
uwal_rpc_flowcontrol_value|int|8,2048|NULL|NULL|
uwal_truncate_interval|int|0,7200|NULL|NULL|
uwal_async_append_switch|bool|0,0|NULL|NULL|
enable_gazelle_performance_mode|bool|0,0|NULL|NULL|
enable_aggr_coerce_type|bool|0,0|NULL|NULL|
enable_default_local_index|bool|0,0|NULL|NULL|
enable_pq|bool|0,0|NULL|NULL|
ivfpq_kreorder|int|0,2147483647|NULL|NULL|
enable_query_parameterization|bool|0,0|NULL|NULL|
max_parameterized_query_stored|int|1,512|NULL|NULL|
enable_lc_xlog_flush_optimize|bool|0,0|NULL|NULL|
[cmserver]
log_dir|string|0,0|NULL|NULL|
log_file_size|int|0,2047|MB|NULL|
log_min_messages|enum|debug5,debug1,log,warning,error,fatal|NULL|NULL|
alarm_report_interval|int|0,2147483647|NULL|NULL|
alarm_component|string|0,0|NULL|NULL|
thread_count|int|2,1000|NULL|NULL|
instance_failover_delay_timeout|int|0,2147483647|NULL|NULL|
instance_heartbeat_timeout|int|1,2147483647|NULL|NULL|
cmserver_ha_connect_timeout|int|0,2147483647|NULL|NULL|
cmserver_ha_heartbeat_timeout|int|1,2147483647|NULL|NULL|
cmserver_ha_status_interval|int|0,2147483647|NULL|NULL|
cmserver_self_vote_timeout|int|0,2147483647|NULL|This parameter works only when cmserver_self_vote_timeout >= cmserver_ha_heartbeat_timeout, otherwise, it will work based on cmserver_ha_heartbeat_timeout.|
enable_transaction_read_only|bool|0,0|NULL|NULL|
datastorage_threshold_check_interval|int|1,2592000|NULL|NULL|
alarm_report_max_count|int|1,2592000|NULL|NULL|
datastorage_threshold_value_check|int|1,99|NULL|NULL|
max_datastorage_threshold_check|int|1,2592000|NULL|NULL|
phony_dead_effective_time|int|1,2147483647|NULL|NULL|
instance_keep_heartbeat_timeout|int|0,2147483647|NULL|NULL|
cm_server_arbitrate_delay_base_time_out|int|0,2147483647|NULL|NULL|
cm_server_arbitrate_delay_incrememtal_time_out|int|0,2147483647|NULL|NULL|
enable_az_auto_switchover|int|0,1|NULL|NULL|
backup_open|int|0,2|NULL|NULL|
cm_auth_method|enum|trust,gss|NULL|NULL|
cm_krb_server_keyfile|string|0,0|NULL|NULL|
switch_rto|int|0,2147483647|NULL|NULL|
force_promote|int|0,1|NULL|NULL|
az_switchover_threshold|int|1,100|NULL|NULL|
az_check_and_arbitrate_interval|int|1,2147483647|NULL|NULL|
az_connect_check_interval|int|1,2147483647|NULL|NULL|
az_connect_check_delay_time|int|1,2147483647|NULL|NULL|
cmserver_demote_delay_on_etcd_fault|int|1,2147483647|NULL|NULL|
instance_phony_dead_restart_interval|int|1800,2147483647|NULL|NULL|
install_type|int|0,2|NULL|NULL|
enable_dcf|bool|0,0|NULL|NULL|
enable_ssl|bool|0,0|NULL|NULL|
ssl_cert_expire_alert_threshold|int|7,180|NULL|NULL|
ssl_cert_expire_check_interval|int|0,2147483647|NULL|NULL|
delay_arbitrate_timeout|int|0,2147483647|NULL|NULL|
ddb_type|int|0,1|NULL|NULL|
ddb_log_level|string|0,0|NULL|NULL|
ddb_log_backup_file_count|int|1,100|NULL|NULL|
ddb_max_log_file_size|string|0,0|NULL|NULL|
ddb_log_suppress_enable|int|0,1|NULL|NULL|
ddb_election_timeout|int|1,600|NULL|NULL|
upgrade_from|int|0,4294967295|NULL|For upgrading, specify which version we are upgrading from.|
[cmagent]
log_dir|string|0,0|NULL|NULL|
log_file_size|int|0,2047|MB|NULL|
log_min_messages|enum|debug5,debug1,log,warning,error,fatal|NULL|NULL|
log_max_size|int|0,2147483647|NULL|NULL|
log_max_count|int|0,10000|NULL|NULL|
log_saved_days|int|0,1000|NULL|NULL|
enable_log_compress|bool|0,0|NULL|NULL|
alarm_report_interval|int|0,2147483647|NULL|NULL|
alarm_report_max_count|int|1,2592000|NULL|NULL|
alarm_component|string|0,0|NULL|NULL|
incremental_build|bool|0,0|NULL|NULL|
agent_report_interval|int|0,2147483647|NULL|NULL|
agent_heartbeat_timeout|int|2,2147483647|NULL|NULL|
agent_connect_timeout|int|0,2147483647|NULL|NULL|
agent_connect_retries|int|0,2147483647|NULL|NULL|
agent_check_interval|int|0,2147483647|NULL|NULL|
agent_kill_instance_timeout|int|0,2147483647|NULL|NULL|
agent_backup_open|int|0,2|NULL|NULL|
log_threshold_check_interval|int|0,2147483647|NULL|NULL|
dilatation_shard_count_for_disk_capacity_alarm|int|0,2147483647|NULL|NULL|
security_mode|bool|0,0|NULL|NULL|
upgrade_from|int|0,4294967295|NULL|For upgrading, specify which version we are upgrading from.|
unix_socket_directory|string|0,0|NULL|NULL|
enable_xc_maintenance_mode|bool|0,0|NULL|NULL|
process_cpu_affinity|int|0,2|NULL|NULL|
agent_phony_dead_check_interval|int|0,2147483647|NULL|NULL|
enable_dcf|bool|0,0|NULL|NULL|
disaster_recovery_type|int|0,2|NULL|NULL|
[lcname]
allow_concurrent_tuple_update|bool|0,0|NULL|NULL|
prefetch_quantity|int|128,131072|kB|NULL|
backwrite_quantity|int|128,131072|kB|NULL|
cstore_prefetch_quantity|int|1024,1048576|kB|NULL|
cstore_backwrite_max_threshold|int|4096,1073741823|kB|NULL|
cstore_backwrite_quantity|int|1024,1048576|kB|NULL|
fast_extend_file_size|int|1024,1048576|kB|NULL|
bgwriter_delay|int|10,10000|ms|NULL|
bgwriter_lru_maxpages|int|0,1000|NULL|NULL|
bgwriter_flush_after|int|0,256|kB|NULL|
autovacuum_naptime|int|1,2147483|s|NULL|
autovacuum_analyze_scale_factor|real|0,100|NULL|NULL|
autovacuum_analyze_threshold|int|0,2147483647|NULL|NULL|
autovacuum_vacuum_scale_factor|real|0,100|NULL|NULL|
autovacuum_vacuum_threshold|int|0,2147483647|NULL|NULL|
enable_data_replicate|bool|0,0|NULL|When this parameter is set on, replication_type must be 0.|
wal_keep_segments|int|2,2147483647|NULL|When the server is turned on or archive log recovery from the checkpoint, the number of reserved log files may be larger than the set value wal_keep_segments. If this parameter is set too low, at the time of the transaction log backup requests, the new transaction log may have been produced coverage request fails, disconnect the master and slave relationship.|
wal_sender_timeout|int|0,2147483647|ms|If the host larger data rebuild operation requires increasing the value of this parameter,the host data at 500G, refer to this parameter is 600. This value can not be greater than the wal_receiver_timeout or database rebuilding timeout parameter.|
wal_writer_delay|int|1,10000|ms|If the time is too long will cause WAL buffers memory shortage, time is too short will cause WAL continue to write, increase disk I/O burden.|
walsender_max_send_size|int|8,2147483647|kB|NULL|
checkpoint_segments|int|1,2147483646|NULL|NULL|
checkpoint_timeout|int|30,3600|s|NULL|
checkpoint_warning|int|0,2147483647|s|NULL|
checkpoint_flush_after|int|0,256|kB|NULL|
checkpoint_wait_timeout|int|2,3600|s|NULL|
vacuum_cost_delay|int|0,100|ms|NULL|
vacuum_cost_limit|int|1,10000|NULL|NULL|
vacuum_cost_page_dirty|int|0,10000|NULL|NULL|
vacuum_cost_page_hit|int|0,10000|NULL|NULL|
vacuum_cost_page_miss|int|0,10000|NULL|NULL|
vacuum_gtt_defer_check_age|int|0,1000000|NULL|NULL|
autovacuum_vacuum_cost_delay|int|-1,100|ms|NULL|
autovacuum_vacuum_cost_limit|int|-1,10000|NULL|NULL|
full_page_writes|bool|0,0|NULL|When full_page_writes set to off, unable to restore the original data when the system crashes, it will cause the database unusable.|
fsync|bool|0,0|NULL|Using the fsync() system function can guarantee that when the operating system exception or hardware crash occurs, you can restore data to a consistent state. When fsync set to off, unable to restore the original data when the system crashes, it will cause the database unusable.|
bulk_read_ring_size|int|256,2147483647|kB|NULL|
bulk_write_ring_size|int|16384,2147483647|kB|NULL|
partition_max_cache_size|int|4096,1073741823|kB|NULL|
partition_mem_batch|int|1,65535|NULL|NULL|
temp_file_limit|int|-1,2147483647|kB|SQL query using a temporary table space when executed unless the system.|
query_mem|int|0,2147483647|kB|Sets the memory to be reserved for a statement.|
maintenance_work_mem|int|1024,2147483647|kB|NULL|
synchronous_commit|enum|local,remote_receive,remote_write,on,off,true,false,yes,no,1,0|NULL|NULL|
work_mem|int|64,2147483647|kB|For complex queries, it may run several concurrent sort or hash operation, each of which can use the amount of memory that this parameter is declared using the temporary file is insufficient. Also, several running sessions could be sorted the same time. Therefore, the total memory usage may be work_mem several times.|
dynamic_memory_quota|int|1,100|NULL|NULL|
temp_buffers|int|100,1073741823|kB|NULL|
max_loaded_cudesc|int|100,1073741823|NULL|NULL|
wal_receiver_buffer_size|int|4096,1047552|kB|NULL|
wal_receiver_status_interval|int|0,2147483|s|NULL|
wal_receiver_timeout|int|0,2147483647|ms|NULL|
wal_receiver_connect_timeout|int|0,2147483|s|NULL|
wal_receiver_connect_retries|int|1,2147483647|NULL|NULL|
data_replicate_buffer_size|int|4096,1072693248|kB|NULL|
max_connections|int|1,262143|NULL|NULL|
max_coordinators|int|2,1024|NULL|NULL|
max_files_per_process|int|25,2147483647|NULL|NULL|
shared_buffers|int|16,1073741823|kB|NULL|
pca_shared_buffers|int|8,1073741823|kB|NULL|
memorypool_size|int|131072,1073741823|kB|NULL|
cstore_buffers|int|16384,1073741823|kB|NULL|
udfworkermemhardlimit|int|0,2147483647|kB|Sets the hard memory limit to be used for fenced UDF.|
wal_buffers|int|-1,262144|kB|Every time a transaction is committed, the contents of WAL buffers are written to disk, it is set to a large value will not bring significant performance gains. If you set it to hundreds of megabytes, you may have written to the disk to improve performance on the server a lot of real-time transaction commits. According to experience, the default value is sufficient for most situations.|
max_wal_senders|int|0,262143|NULL|Check whether the new value of max_wal_senders is less than max_connections and wal_level is archive, hot_standby or logical, otherwise the gaussdb will start failed.|
max_replication_slots|int|0,262143|NULL|NULL|
autovacuum_freeze_max_age|int64|100000,576460752303423487|NULL|NULL|
autovacuum_max_workers|int|0,262143|NULL|NULL|
track_activity_query_size|int|100,102400|NULL|NULL|
gms_stats_history_retention|int|-1,365000|d|NULL|
event_source|string|0,0|NULL|NULL|
memorypool_enable|bool|0,0|NULL|NULL|
enable_memory_limit|bool|0,0|NULL|NULL|
enable_dump_trigger_definer|bool|0,0|NULL|NULL|
datanode_heartbeat_interval|int|1000,60000|ms|The value is best configured less than half of the wal_receiver_timeout and wal_sender_timeout.|
cost_weight_index|real|1e-10,1e+10|NULL|NULL|
default_limit_rows|real|-100,1.79769e+308|NULL|NULL|
enable_auto_explain|bool|0,0|NULL|NULL|
auto_explain_level|enum|off,log,notice|NULL|NULL|
sql_note|bool|0,0|NULL|NULL|
max_error_count|int|0,65535|NULL|NULL|
[end]
