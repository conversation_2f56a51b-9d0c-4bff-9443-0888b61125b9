/* -------------------------------------------------------------------------
 *
 * kwlist.h
 *
 * The keyword list is kept in its own source file for possible use by
 * automatic tools.  The exact representation of a keyword is determined
 * by the PG_KEYWORD macro, which is not defined in this file; it can
 * be defined by the caller for special purposes.
 *
 * Portions Copyright (c) 1996-2012, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 * Portions Copyright (c) 2010-2012 Postgres-XC Development Group
 * Portions Copyright (c) 2021, openGauss Contributors
 *
 * IDENTIFICATION
 *	  src/include/parser/kwlist.h
 *
 * -------------------------------------------------------------------------
 */

/* there is deliberately not an #ifndef KWLIST_H here */

/*
 * List of keyword (name, token-value, category, direct-label-status) entries.
 *
 * Note: gen_keywordlist.pl requires the entries to appear in ASCII order.
 */

/* name, value, category */
PG_KEYWORD("abort", ABORT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("absolute", ABSOLUTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("access", ACCESS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("account", ACCOUNT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("action", ACTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("add", ADD_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("admin", ADMIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("after", AFTER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("aggregate", AGGREGATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("algorithm", ALGORITHM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("all", ALL, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("also", ALSO, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("alter", ALTER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("always", ALWAYS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("analyse", ANALYSE, RESERVED_KEYWORD, DIRECT_LABEL) /* British spelling */
PG_KEYWORD("analyze", ANALYZE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("and", AND, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("any", ANY, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("app", APP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("append", APPEND, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("apply", APPLY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("archive", ARCHIVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("array", ARRAY, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("as", AS, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("asc", ASC, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("asof", ASOF_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("assertion", ASSERTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("assignment", ASSIGNMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("asymmetric", ASYMMETRIC, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("at", AT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("attribute", ATTRIBUTE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("audit", AUDIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("authid", AUTHID, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("authorization", AUTHORIZATION, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("auto_increment", AUTO_INCREMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("autoextend", AUTOEXTEND, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("automapped", AUTOMAPPED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("backward", BACKWARD, UNRESERVED_KEYWORD, DIRECT_LABEL)
#ifdef PGXC
PG_KEYWORD("barrier", BARRIER, UNRESERVED_KEYWORD, DIRECT_LABEL)
#endif
PG_KEYWORD("before", BEFORE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("begin", BEGIN_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("begin_non_anoyblock", BEGIN_NON_ANOYBLOCK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("between", BETWEEN, COL_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("bigint", BIGINT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("binary", BINARY, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("binary_double", BINARY_DOUBLE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("binary_double_infinity", BINARY_DOUBLE_INF, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("binary_double_nan", BINARY_DOUBLE_NAN, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("binary_integer", BINARY_INTEGER, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("bit", BIT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("blanks", BLANKS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("blob", BLOB_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("blockchain", BLOCKCHAIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("body", BODY_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("boolean", BOOLEAN_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("both", BOTH, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("bucketcnt", BUCKETCNT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("buckets", BUCKETS, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("build", BUILD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("by", BY, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("byte", BYTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("byteawithoutorder", BYTEAWITHOUTORDER, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("byteawithoutorderwithequal", BYTEAWITHOUTORDERWITHEQUAL, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cache", CACHE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("call", CALL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("called", CALLED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cancelable", CANCELABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cascade", CASCADE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cascaded", CASCADED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("case", CASE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cast", CAST, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("catalog", CATALOG_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("catalog_name", CATALOG_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("chain", CHAIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("change", CHANGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("char", CHAR_P, COL_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("character", CHARACTER, COL_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("characteristics", CHARACTERISTICS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("characterset", CHARACTERSET, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("charset", CHARSET, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("check", CHECK, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("checkident", CHECKIDENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("checkpoint", CHECKPOINT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("class", CLASS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("class_origin", CLASS_ORIGIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
#ifdef PGXC
PG_KEYWORD("clean", CLEAN, UNRESERVED_KEYWORD, DIRECT_LABEL)
#endif
PG_KEYWORD("client", CLIENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("client_master_key", CLIENT_MASTER_KEY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("client_master_keys", CLIENT_MASTER_KEYS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("clob", CLOB, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("close", CLOSE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cluster", CLUSTER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("clustered", TSQL_CLUSTERED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("coalesce", COALESCE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("collate", COLLATE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("collation", COLLATION, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("column", COLUMN, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("column_encryption_key", COLUMN_ENCRYPTION_KEY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("column_encryption_keys", COLUMN_ENCRYPTION_KEYS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("column_name", COLUMN_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("columns", COLUMNS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("columnstore", TSQL_COLUMNSTORE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("comment", COMMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("comments", COMMENTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("commit", COMMIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("committed", COMMITTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("compact", COMPACT, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("compatible_illegal_chars", COMPATIBLE_ILLEGAL_CHARS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("compile", COMPILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("complete", COMPLETE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("completion", COMPLETION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("compress", COMPRESS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("concurrently", CONCURRENTLY, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("condition", CONDITION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("configuration", CONFIGURATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("connect", CONNECT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("connection", CONNECTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("consistent", CONSISTENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constant", CONSTANT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constraint", CONSTRAINT, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constraint_catalog", CONSTRAINT_CATALOG, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constraint_name", CONSTRAINT_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constraint_schema", CONSTRAINT_SCHEMA, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constraints", CONSTRAINTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("constructor", CONSTRUCTOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("content", CONTENT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("continue", CONTINUE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("contview", CONTVIEW, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("conversion", CONVERSION_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("convert", CONVERT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("coordinator", COORDINATOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("coordinators", COORDINATORS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("copy", COPY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cost", COST, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("create", CREATE, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("cross", CROSS, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("csn", CSN, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("csv", CSV, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cube", CUBE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current", CURRENT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_catalog", CURRENT_CATALOG, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_date", CURRENT_DATE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_role", CURRENT_ROLE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_schema", CURRENT_SCHEMA, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_time", CURRENT_TIME, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_timestamp", CURRENT_TIMESTAMP, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("current_user", CURRENT_USER, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cursor", CURSOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cursor_name", CURSOR_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("cycle", CYCLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("data", DATA_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("database", DATABASE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("datafile", DATAFILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("datanode", DATANODE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("datanodes", DATANODES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("datatype_cl", DATATYPE_CL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("date", DATE_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("date_format", DATE_FORMAT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("day", DAY_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("day_hour", DAY_HOUR_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("day_minute", DAY_MINUTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("day_second", DAY_SECOND_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dbcc", DBCC, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dbcompatibility", DBCOMPATIBILITY_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("deallocate", DEALLOCATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dec", DEC, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("decimal", DECIMAL_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("declare", DECLARE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("decode", DECODE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("default", DEFAULT, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("defaults", DEFAULTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("deferrable", DEFERRABLE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("deferred", DEFERRED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("definer", DEFINER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("delete", DELETE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("delimiter", DELIMITER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("delimiters", DELIMITERS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("delta", DELTA, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("deltamerge", DELTAMERGE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dense_rank", DENSE_RANK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("desc", DESC, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("deterministic", DETERMINISTIC, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("diagnostics", DIAGNOSTICS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dictionary", DICTIONARY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("direct", DIRECT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("directory", DIRECTORY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("disable", DISABLE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("discard", DISCARD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("disconnect", DISCONNECT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("distinct", DISTINCT, RESERVED_KEYWORD, DIRECT_LABEL)
#ifdef PGXC
PG_KEYWORD("distribute", DISTRIBUTE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("distribution", DISTRIBUTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
#endif
PG_KEYWORD("do", DO, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("document", DOCUMENT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("domain", DOMAIN_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("double", DOUBLE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("drop", DROP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("dumpfile", DUMPFILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("duplicate", DUPLICATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("each", EACH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("elastic", ELASTIC, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("else", ELSE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("enable", ENABLE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("enclosed", ENCLOSED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("encoding", ENCODING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("encrypted", ENCRYPTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("encrypted_value", ENCRYPTED_VALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("encryption", ENCRYPTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("encryption_type", ENCRYPTION_TYPE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("end", END_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ends", ENDS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("enforced", ENFORCED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("enum", ENUM_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("eol", EOL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("error", ERROR_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("errors", ERRORS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("escape", ESCAPE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("escaped", ESCAPED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("escaping", ESCAPING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("event", EVENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("events", EVENTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("every", EVERY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("except", EXCEPT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("exchange", EXCHANGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("exclude", EXCLUDE, UNRESERVED_KEYWORD, DIRECT_LABEL)
#ifndef ENABLE_MULTIPLE_NODES
PG_KEYWORD("excluded", EXCLUDED, RESERVED_KEYWORD, DIRECT_LABEL)
#endif
PG_KEYWORD("excluding", EXCLUDING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("exclusive", EXCLUSIVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("execute", EXECUTE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("exists", EXISTS, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("expired", EXPIRED_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("explain", EXPLAIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("extension", EXTENSION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("external", EXTERNAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("extract", EXTRACT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("false", FALSE_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("family", FAMILY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fast", FAST, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("features", FEATURES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fenced", FENCED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fetch", FETCH, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("fields", FIELDS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fileheader", FILEHEADER_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fill_missing_fields", FILL_MISSING_FIELDS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("filler", FILLER, UNRESERVED_KEYWORD, DIRECT_LABEL) 
PG_KEYWORD("filter", FILTER, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("final", FINAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("first", FIRST_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("fixed", FIXED_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("float", FLOAT_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("following", FOLLOWING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("follows", FOLLOWS_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("for", FOR, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("force", FORCE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("foreign", FOREIGN, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("formatter", FORMATTER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("forward", FORWARD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("freeze", FREEZE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("from", FROM, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("full", FULL, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("function", FUNCTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("functions", FUNCTIONS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("generated", GENERATED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("get", GET, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("global", GLOBAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("grant", GRANT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("granted", GRANTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("greatest", GREATEST, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("group", GROUP_P, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("grouping", GROUPING_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("groupparent", GROUPPARENT, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("handler", HANDLER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("having", HAVING, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("hdfsdirectory", HDFSDIRECTORY, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("header", HEADER_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("hold", HOLD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("hour", HOUR_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("hour_minute", HOUR_MINUTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("hour_second", HOUR_SECOND_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
/* new key-word for ALTER ROLE */
PG_KEYWORD("identified", IDENTIFIED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("identity", IDENTITY_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("if", IF_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ignore", IGNORE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ignore_extra_data", IGNORE_EXTRA_DATA, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ilike", ILIKE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("imcstored", IMCSTORED, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("immediate", IMMEDIATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("immutable", IMMUTABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("implicit", IMPLICIT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("in", IN_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("include", INCLUDE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("including", INCLUDING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("increment", INCREMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("incremental", INCREMENTAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("index", INDEX, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("indexes", INDEXES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("infile", INFILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("infinite", INFINITE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("inherit", INHERIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("inherits", INHERITS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("initial", INITIAL_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("initially", INITIALLY, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("initrans", INITRANS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("inline", INLINE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("inner", INNER_P, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("inout", INOUT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("input", INPUT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("insensitive", INSENSITIVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("insert", INSERT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("instead", INSTEAD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("int", INT_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("integer", INTEGER, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("internal", INTERNAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("intersect", INTERSECT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("interval", INTERVAL, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("into", INTO, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("invisible", INVISIBLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("invoker", INVOKER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ip", IP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("is", IS, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("isnull", ISNULL, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("isolation", ISOLATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("join", JOIN, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("json_exists", JSON_EXISTS, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("keep", KEEP, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("key", KEY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("key_path", KEY_PATH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("key_store", KEY_STORE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("kill", KILL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("label", LABEL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("language", LANGUAGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("large", LARGE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("last", LAST_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("lateral", LATERAL_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("lc_collate", LC_COLLATE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("lc_ctype", LC_CTYPE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("leading", LEADING, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("leakproof", LEAKPROOF, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("least", LEAST, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("left", LEFT, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("less", LESS, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("level", LEVEL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("like", LIKE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("limit", LIMIT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("lines", LINES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("list", LIST, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("listen", LISTEN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("load", LOAD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("local", LOCAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("localtime", LOCALTIME, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("localtimestamp", LOCALTIMESTAMP, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("location", LOCATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("lock", LOCK_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("locked", LOCKED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("log", LOG_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("logging", LOGGING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("login_any", LOGIN_ANY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("login_failure", LOGIN_FAILURE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("login_success", LOGIN_SUCCESS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("logout", LOGOUT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("loop", LOOP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("map", MAP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("mapping", MAPPING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("masking", MASKING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("master", MASTER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("match", MATCH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("matched", MATCHED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("materialized", MATERIALIZED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("maxextents", MAXEXTENTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("maxsize", MAXSIZE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("maxtrans", MAXTRANS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("maxvalue", MAXVALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("member", MEMBER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("merge", MERGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("message_text", MESSAGE_TEXT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("method", METHOD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("minextents", MINEXTENTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("minus", MINUS_P, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("minute", MINUTE_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("minute_second", MINUTE_SECOND_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("minvalue", MINVALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("mode", MODE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("model", MODEL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("modify", MODIFY_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("month", MONTH_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("move", MOVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("movement", MOVEMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("mysql_errno", MYSQL_ERRNO, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("name", NAME_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("names", NAMES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nan", NAN_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("national", NATIONAL, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("natural", NATURAL, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nchar", NCHAR, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("next", NEXT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("no", NO, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("no_infomsgs", NO_INFOMSGS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nocompress", NOCOMPRESS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nocycle", NOCYCLE, RESERVED_KEYWORD, DIRECT_LABEL)
#ifdef PGXC
PG_KEYWORD("node", NODE, UNRESERVED_KEYWORD, DIRECT_LABEL)
#endif
PG_KEYWORD("nologging", NOLOGGING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nomaxvalue", NOMAXVALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nominvalue", NOMINVALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nonclustered", TSQL_NONCLUSTERED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("none", NONE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("noreseed", NORESEED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("not", NOT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("nothing", NOTHING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("notify", NOTIFY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("notnull", NOTNULL, TYPE_FUNC_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("novalidate", NOVALIDATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nowait", NOWAIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nth_value", NTH_VALUE_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("null", NULL_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nullcols", NULLCOLS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nullif", NULLIF, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nulls", NULLS_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("number", NUMBER_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("numeric", NUMERIC, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("numstr", NUMSTR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nvarchar", NVARCHAR, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nvarchar2", NVARCHAR2, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("nvl", NVL, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("object", OBJECT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("of", OF, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("off", OFF, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("offset", OFFSET, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("oids", OIDS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("on", ON, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("only", ONLY, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("operator", OPERATOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("optimization", OPTIMIZATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("option", OPTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("optionally", OPTIONALLY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("options", OPTIONS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("or", OR, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("order", ORDER, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("out", OUT_P, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("outer", OUTER_P, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("outfile", OUTFILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("over", OVER, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("overlaps", OVERLAPS, TYPE_FUNC_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("overlay", OVERLAY, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("owned", OWNED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("owner", OWNER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("package", PACKAGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("packages", PACKAGES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("parallel_enable", PARALLEL_ENABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("parser", PARSER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("partial", PARTIAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("partition", PARTITION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("partitions", PARTITIONS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("passing", PASSING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("password", PASSWORD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("pctfree", PCTFREE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("per", PER_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("percent", TSQL_PERCENT, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("performance", PERFORMANCE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("perm", PERM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("persisted", TSQL_PERSISTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("pipelined", PIPELINED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("placing", PLACING, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("plan", PLAN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("plans", PLANS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("policy", POLICY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("pool", POOL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("position", POSITION, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("precedes", PRECEDES_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("preceding", PRECEDING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("precision", PRECISION, COL_NAME_KEYWORD, AS_LABEL)
PG_KEYWORD("predict", PREDICT, UNRESERVED_KEYWORD, DIRECT_LABEL)
/* PGXC_BEGIN */
PG_KEYWORD("preferred", PREFERRED, UNRESERVED_KEYWORD, DIRECT_LABEL)
/* PGXC_END */
PG_KEYWORD("prefix", PREFIX, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("prepare", PREPARE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("prepared", PREPARED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("preserve", PRESERVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("primary", PRIMARY, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("prior", PRIOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("priorer", PRIORER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("private", PRIVATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("privilege", PRIVILEGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("privileges", PRIVILEGES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("procedural", PROCEDURAL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("procedure", PROCEDURE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("profile", PROFILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("publication", PUBLICATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("publish", PUBLISH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("purge", PURGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("query", QUERY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("quote", QUOTE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("randomized", RANDOMIZED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("range", RANGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ratio", RATIO, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("raw", RAW, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("read", READ, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("real", REAL, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reassign", REASSIGN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rebuild", REBUILD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("recheck", RECHECK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("recursive", RECURSIVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("recyclebin", RECYCLEBIN, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("redisanyvalue", REDISANYVALUE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ref", REF, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("references", REFERENCES, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("refresh", REFRESH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reindex", REINDEX, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reject", REJECT_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("relative", RELATIVE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("release", RELEASE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reloptions", RELOPTIONS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("remote", REMOTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("remove", REMOVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rename", RENAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("repeat", REPEAT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("repeatable", REPEATABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("replace", REPLACE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("replica", REPLICA, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reseed", RESEED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reset", RESET, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("resize", RESIZE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("resource", RESOURCE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("respect", RESPECT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("restart", RESTART, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("restrict", RESTRICT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("result", RESULT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("return", RETURN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("returned_sqlstate", RETURNED_SQLSTATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("returning", RETURNING, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("returns", RETURNS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("reuse", REUSE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("revoke", REVOKE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("right", RIGHT, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("role", ROLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("roles", ROLES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rollback", ROLLBACK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rollup", ROLLUP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rotate", ROTATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rotation", ROTATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("row", ROW, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("row_count", ROW_COUNT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rows", ROWS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rowtype", ROWTYPE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("rule", RULE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sample", SAMPLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("savepoint", SAVEPOINT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("schedule", SCHEDULE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("schema", SCHEMA, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("schema_name", SCHEMA_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("scroll", SCROLL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("search", SEARCH, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("second", SECOND_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("security", SECURITY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("select", SELECT, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("self", SELF, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("separator", SEPARATOR_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sequence", SEQUENCE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sequences", SEQUENCES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("serializable", SERIALIZABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("server", SERVER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("session", SESSION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("session_user", SESSION_USER, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("set", SET, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("setof", SETOF, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sets", SETS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("share", SHARE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("shippable", SHIPPABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("show", SHOW, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("shrink", SHRINK, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("shutdown", SHUTDOWN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("siblings", SIBLINGS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("similar", SIMILAR, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("simple", SIMPLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("size", SIZE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("skip", SKIP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("slave", SLAVE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("slice", SLICE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("smalldatetime", SMALLDATETIME, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("smalldatetime_format", SMALLDATETIME_FORMAT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("smallint", SMALLINT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("snapshot", SNAPSHOT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("some", SOME, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("source", SOURCE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("space", SPACE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("specification", SPECIFICATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("spill", SPILL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("split", SPLIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sql", SQL_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stable", STABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stacked", STACKED_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("standalone", STANDALONE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("start", START, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("starting", STARTING, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("starts", STARTS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("statement", STATEMENT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("statement_id", STATEMENT_ID, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("static", STATIC_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("statistics", STATISTICS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stdin", STDIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stdout", STDOUT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("storage", STORAGE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("store", STORE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stored", STORED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stratify", STRATIFY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("stream", STREAM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("strict", STRICT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("strip", STRIP_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("subclass_origin", SUBCLASS_ORIGIN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("subpartition", SUBPARTITION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("subpartitions", SUBPARTITIONS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("subscription", SUBSCRIPTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("substring", SUBSTRING, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("symmetric", SYMMETRIC, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("synonym", SYNONYM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sys_refcursor", SYS_REFCURSOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sysdate", SYSDATE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("sysid", SYSID, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("system", SYSTEM_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("table", TABLE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("table_name", TABLE_NAME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tables", TABLES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tablesample", TABLESAMPLE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tablespace", TABLESPACE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("target", TARGET, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("temp", TEMP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("template", TEMPLATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("temporary", TEMPORARY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("terminated", TERMINATED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("text", TEXT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("than", THAN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("then", THEN, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("ties", TIES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("time", TIME, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("time_format", TIME_FORMAT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timecapsule", TIMECAPSULE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timestamp", TIMESTAMP, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timestamp_format", TIMESTAMP_FORMAT_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timestampdiff", TIMESTAMPDIFF, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timezone_hour", TIMEZONE_HOUR_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("timezone_minute", TIMEZONE_MINUTE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tinyint", TINYINT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("to", TO, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("top", TSQL_TOP, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("trailing", TRAILING, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("transaction", TRANSACTION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("transform", TRANSFORM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("treat", TREAT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("trigger", TRIGGER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("trim", TRIM, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("true", TRUE_P, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("truncate", TRUNCATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("trusted", TRUSTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tsfield", TSFIELD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tstag", TSTAG, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("tstime", TSTIME, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("type", TYPE_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("types", TYPES_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unbounded", UNBOUNDED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("uncommitted", UNCOMMITTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("under", UNDER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unencrypted", UNENCRYPTED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unimcstored", UNIMCSTORED, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("union", UNION, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("unique", UNIQUE, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unknown", UNKNOWN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unlimited", UNLIMITED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unlisten", UNLISTEN, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unlock", UNLOCK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unlogged", UNLOGGED, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("until", UNTIL, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("unusable", UNUSABLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("update", UPDATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("use", USE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("useeof", USEEOF, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("user", USER, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("using", USING, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("vacuum", VACUUM, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("valid", VALID, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("validate", VALIDATE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("validation", VALIDATION, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("validator", VALIDATOR, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("value", VALUE_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("values", VALUES, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("varchar", VARCHAR, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("varchar2", VARCHAR2, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("variables", VARIABLES, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("variadic", VARIADIC, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("varray", VARRAY, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("varying", VARYING, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("vcgroup", VCGROUP, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("verbose", VERBOSE, TYPE_FUNC_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("verify", VERIFY, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("version", VERSION_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("view", VIEW, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("visible", VISIBLE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("volatile", VOLATILE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("wait", WAIT, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("warnings", WARNINGS, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("weak", WEAK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("when", WHEN, RESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("where", WHERE, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("while", WHILE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("whitespace", WHITESPACE_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("window", WINDOW, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("with", WITH, RESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("within", WITHIN, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("without", WITHOUT, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("work", WORK, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("workload", WORKLOAD, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("wrapper", WRAPPER, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("write", WRITE, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xml", XML_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("xmlattributes", XMLATTRIBUTES, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlconcat", XMLCONCAT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlelement", XMLELEMENT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlexists", XMLEXISTS, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlforest", XMLFOREST, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlparse", XMLPARSE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlpi", XMLPI, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlroot", XMLROOT, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("xmlserialize", XMLSERIALIZE, COL_NAME_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("year", YEAR_P, UNRESERVED_KEYWORD, AS_LABEL)
PG_KEYWORD("year_month", YEAR_MONTH_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("yes", YES_P, UNRESERVED_KEYWORD, DIRECT_LABEL)
PG_KEYWORD("zone", ZONE, UNRESERVED_KEYWORD, DIRECT_LABEL)
