# openGauss/PostgreSQL WAL 归档连续性调研报告

## 1. 调研背景

### 1.1 业务需求
- **目标**：将本地已归档的 WAL 文件上传到 S3，释放本地磁盘空间
- **挑战**：主从切换时可能出现归档间隙，影响数据完整性
- **环境**：Trove 管理的 openGauss/PostgreSQL 主从集群，Waltz 高可用服务监控

### 1.2 核心问题
1. 主从切换时如何保证归档连续性？
2. 如何避免与正常归档流程冲突？
3. 如何在现有架构下实现最简化的解决方案？
4. WAL 文件保留策略如何配置以支持故障恢复？

## 2. 技术背景分析

### 2.1 PostgreSQL 版本演进与功能差异

**版本演进时间线：**
```
2013-04: PostgreSQL 9.2.4
         - archive_mode: off/on (布尔值)
         - 无 pg_stat_archiver 视图
         - openGauss 基础版本

2014-12: PostgreSQL 9.4.0
         - 引入 pg_stat_archiver 视图
         - 提供归档状态监控

2016-01: PostgreSQL 9.5.0
         - 引入 archive_mode=always
         - 支持备库归档

2020-09: PostgreSQL 13.3
         - 完整支持三种模式
         - 成熟的归档机制

2020-06: openGauss 开源
         - 基于 9.2.4 独立演进
         - 专注企业级功能
```

### 2.2 功能支持对比

| 功能特性 | PostgreSQL 9.2.4 | PostgreSQL 13.3 | openGauss |
|----------|-------------------|------------------|-----------|
| **archive_mode 选项** | `off`, `on` | `off`, `on`, `always` | `off`, `on` |
| **pg_stat_archiver 视图** | ❌ 不支持 | ✅ 支持 | ❌ 不支持 |
| **备库归档** | ❌ 不支持 | ✅ 支持 (always模式) | ❌ 不支持 |
| **Archive Slot** | ❌ 不支持 | ❌ 不支持 | ✅ 支持 |

## 3. 归档机制深度分析

### 3.1 archive_mode=on 工作原理

#### 源码逻辑对比

**PostgreSQL 13.3 源码逻辑：**
```c
// 在 RestoreArchivedFile 和 walreceiver 中
if (XLogArchiveMode != ARCHIVE_MODE_ALWAYS)
    XLogArchiveForceDone(xlogfname);  // 创建 .done 文件，不归档
else
    XLogArchiveNotify(xlogfname);     // 创建 .ready 文件，需要归档
```

**openGauss 源码逻辑：**
```cpp
// 在 walrcvwriter.cpp 中
/*
 * Create .done file forcibly to prevent the restored segment from
 * being archived again later.
 */
XLogFileName(xlogfname, MAXFNAMELEN, recvFileTLI, recvSegNo);
XLogArchiveForceDone(xlogfname);  // 总是创建 .done，不归档
```

#### 工作流程说明

**openGauss archive_mode=on 模式流程：**
```
1. 主库产生 WAL 001
2. 主库归档 WAL 001 到 S3
3. 主库通过流复制发送 WAL 001 到备库
4. 备库接收 WAL 001 到 pg_wal/ 目录
5. 备库对接收的 WAL 001 调用 XLogArchiveForceDone()
6. 备库创建 pg_wal/archive_status/001.done 文件
7. 备库永远不会归档接收到的文件

主库故障时：
8. 备库提升为主库
9. 新主库开始产生新的 WAL 002
10. 新主库归档 WAL 002
11. 可能存在归档间隙！（如果主库故障前有未归档的 WAL）
```

### 3.2 archive_mode=always 工作原理（仅 PostgreSQL 9.5+）

**PostgreSQL 13.3 archive_mode=always 模式流程：**
```
1. 主库产生 WAL 001
2. 主库归档 WAL 001 到 S3
3. 主库通过流复制发送 WAL 001 到备库
4. 备库接收 WAL 001 到 pg_wal/ 目录
5. 备库对接收的 WAL 001 调用 XLogArchiveNotify()
6. 备库创建 pg_wal/archive_status/001.ready 文件
7. 备库归档进程发现 .ready 文件，归档 WAL 001 到 S3
8. 备库归档完成后创建 001.done 文件
9. 同一文件被归档两次（重复归档）

主库故障时：
10. 备库提升为主库
11. 新主库开始产生新的 WAL 002
12. 新主库继续归档 WAL 002
13. 归档连续，无间隙！
```

## 4. WAL 文件保留策略

### 4.1 关键配置参数

| 参数名 | 作用 | 默认值 | 推荐值 |
|--------|------|--------|--------|
| `wal_keep_segments` | 保留的 WAL 段数量 | 0 | 64-128 |
| `wal_keep_size` | 保留的 WAL 大小 (PG 13+) | 0 | 1-2GB |
| `max_wal_size` | WAL 最大大小 | 1GB | 2-4GB |
| `min_wal_size` | WAL 最小大小 | 80MB | 256MB |
| `checkpoint_timeout` | 检查点超时时间 | 5min | 10-15min |

### 4.2 生产环境推荐配置

#### openGauss 配置
```sql
-- postgresql.conf 配置
wal_level = hot_standby
wal_keep_segments = 128          -- 保留 128 个 WAL 段（约 2GB）
max_wal_size = 4GB
min_wal_size = 512MB
checkpoint_timeout = 15min
archive_timeout = 300
enable_xlog_prune = off          -- 禁用自动 WAL 清理
max_size_for_xlog_prune = 2GB    -- WAL 清理阈值

-- 动态调整
gs_guc reload -D $PGDATA -c "wal_keep_segments=128"
```

#### PostgreSQL 13.3 配置
```sql
-- postgresql.conf 配置
wal_level = replica
wal_keep_size = 2GB              -- 保留 2GB 的 WAL 文件
max_wal_size = 4GB               -- WAL 最大 4GB
min_wal_size = 512MB             -- WAL 最小 512MB
checkpoint_timeout = 15min       -- 15分钟检查点超时
archive_timeout = 300            -- 5分钟强制归档

-- 动态调整（无需重启）
ALTER SYSTEM SET wal_keep_size = '2GB';
SELECT pg_reload_conf();
```

### 4.3 WAL 保留监控

#### 监控脚本
```bash
#!/bin/bash
# wal_retention_monitor.sh

PGDATA="$1"

check_wal_retention() {
    echo "=== WAL Retention Status Check ==="
    
    # 1. 检查当前 WAL 文件数量
    local wal_count=$(find "$PGDATA/pg_wal" -name "[0-9A-F]*" -type f | wc -l)
    echo "Current WAL files: $wal_count"
    
    # 2. 检查 WAL 目录大小
    local wal_size=$(du -sh "$PGDATA/pg_wal" | cut -f1)
    echo "WAL directory size: $wal_size"
    
    # 3. 检查最老的 WAL 文件
    local oldest_wal=$(find "$PGDATA/pg_wal" -name "[0-9A-F]*" -type f -printf '%T@ %p\n' | sort -n | head -1 | cut -d' ' -f2-)
    if [[ -n "$oldest_wal" ]]; then
        local oldest_time=$(stat -c %Y "$oldest_wal")
        local current_time=$(date +%s)
        local age_minutes=$(( (current_time - oldest_time) / 60 ))
        echo "Oldest WAL file: $(basename $oldest_wal) (${age_minutes} minutes old)"
    fi
    
    # 4. 告警检查
    if [[ $wal_count -lt 10 ]]; then
        echo "WARNING: WAL file count is very low ($wal_count)"
    fi
    
    if [[ $age_minutes -lt 60 ]]; then
        echo "WARNING: Oldest WAL file is only ${age_minutes} minutes old"
    fi
}

check_wal_retention
```

## 5. 方案调研与对比

### 5.1 候选方案分析

#### 方案1：archive_mode=on + Trove 异步补偿（推荐）

**架构说明：**
```
Waltz监控 → Trove API → 执行主从切换
                ↓
            发布事件到 RabbitMQ
                ↓
        Archive Service 异步处理
                ↓
        检查并补偿缺失的 WAL 文件
                ↓
            上传到 S3
```

**优势：**
- ✅ 基础设施简单（无需共享存储）
- ✅ 完全解耦（归档逻辑独立于主从切换）
- ✅ 复用现有基础设施（RabbitMQ、oslo.messaging）
- ✅ 异步处理（不影响切换性能）
- ✅ 运维友好（独立服务，易于监控）

**劣势：**
- ⚠️ 需要开发 Trove 集成
- ⚠️ 依赖消息队列可靠性

#### 方案2：openGauss Archive Slot（不推荐）

**架构说明：**
```
Primary DB → Archive Slot → 共享存储(NFS/NAS) → S3存储
Standby DB → Archive Slot（切换时）
```

**优势：**
- ✅ openGauss 原生支持
- ✅ LSN 精确管理
- ✅ 自动连续性保证

**劣势：**
- ❌ 需要共享存储基础设施
- ❌ 运维复杂度高
- ❌ 单点故障风险
- ❌ 性能可能受影响

### 5.2 方案对比评分

| 评估维度 | archive_mode=on + Trove | Archive Slot |
|----------|-------------------------|--------------|
| **基础设施复杂度** | ⭐⭐ (简单) | ⭐⭐⭐⭐⭐ (复杂) |
| **实现复杂度** | ⭐⭐⭐ (中等) | ⭐⭐⭐⭐ (较高) |
| **运维复杂度** | ⭐⭐ (简单) | ⭐⭐⭐⭐⭐ (复杂) |
| **可靠性** | ⭐⭐⭐⭐ (高) | ⭐⭐⭐ (中等) |
| **性能影响** | ⭐⭐ (小) | ⭐⭐⭐ (中等) |
| **扩展性** | ⭐⭐⭐⭐ (好) | ⭐⭐⭐ (一般) |
| **总分** | **8.5/10** | **5.0/10** |

## 6. 推荐方案详细设计

### 6.1 架构设计

#### 核心组件
1. **Trove 事件发布器**：主从切换时发布事件到 RabbitMQ
2. **归档连续性服务**：独立服务，监听切换事件并处理归档间隙
3. **冲突协调机制**：避免与正常归档流程冲突

#### 主从切换归档补偿流程

```
步骤1: Waltz 触发主从切换
步骤2: Trove 执行数据库切换
步骤3: Trove 发布切换完成事件到 RabbitMQ

异步处理开始：
步骤4: Archive Service 接收切换事件
步骤5: 等待60秒（确保切换稳定）
步骤6: 获取 S3 最后归档的 WAL 文件
步骤7: 获取新主库 WAL 文件列表
步骤8: 计算缺失的 WAL 文件

处理缺失文件：
步骤9: 对每个缺失文件：
        - 从新主库读取 WAL 文件内容
        - 上传到 S3
        - 更新处理状态
步骤10: 发布处理完成事件
```

### 6.2 核心实现

#### 缺失 WAL 检测逻辑
```python
async def find_missing_wals(self, instance_id, new_primary_node):
    """查找缺失的WAL文件"""
    
    # 1. 获取S3最后归档的文件（简单文件名比较）
    s3_last_wal = await self._get_s3_last_wal_simple(instance_id)
    
    # 2. 获取新主库的WAL文件
    # 优先级1: 本地归档目录（已归档但未上传S3）
    local_archived = await self._get_local_archived_files(instance_id, new_primary_node)
    
    # 优先级2: pg_wal目录（已完成但未归档）
    pg_wal_files = await self._get_pg_wal_done_files(instance_id, new_primary_node)
    
    # 3. 计算缺失文件
    missing_wals = []
    all_candidates = local_archived + pg_wal_files
    
    for wal_file in all_candidates:
        if self._is_wal_after(wal_file, s3_last_wal):
            missing_wals.append(wal_file)
    
    return missing_wals[:10]  # 限制处理数量，避免过载
```

#### 冲突避免机制
```python
class ArchiveCoordinator:
    """归档协调器，避免重复处理"""

    def __init__(self, redis_client):
        self.redis = redis_client

    @contextmanager
    def archive_lock(self, instance_id, wal_file):
        """获取WAL文件的归档锁"""

        lock_key = f"archive_lock:{instance_id}:{wal_file}"
        lock_value = f"{time.time()}:{os.getpid()}"

        try:
            # 尝试获取锁（5分钟超时）
            if self.redis.set(lock_key, lock_value, nx=True, ex=300):
                yield True
            else:
                yield False  # 其他进程正在处理
        finally:
            # 释放锁
            current_lock = self.redis.get(lock_key)
            if current_lock and current_lock.decode() == lock_value:
                self.redis.delete(lock_key)
```

### 6.3 部署配置

#### 服务配置
```yaml
# trove-archive-continuity.conf
[DEFAULT]
debug = True
log_file = /var/log/trove-archive/service.log

[oslo_messaging_rabbit]
# 复用 Trove 的 RabbitMQ 配置
rabbit_host = *********
rabbit_port = 5672
rabbit_userid = openstack
rabbit_password = password

[archive_continuity]
enabled = true
check_delay_seconds = 60        # 切换后等待时间
max_missing_files = 10          # 最多处理文件数
timeout_seconds = 300           # 处理超时时间

[s3_config]
bucket = trove-wal-archive
region = us-east-1
access_key = your_access_key
secret_key = your_secret_key

[redis]
host = 127.0.0.1
port = 6379
db = 0
```

#### systemd 服务
```ini
# /etc/systemd/system/trove-archive-continuity.service
[Unit]
Description=Trove Archive Continuity Service
After=network.target rabbitmq-server.service redis.service

[Service]
Type=simple
User=trove
Group=trove
ExecStart=/usr/bin/python3 /usr/local/bin/trove-archive-continuity
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 7. 监控和运维

### 7.1 关键监控指标

| 指标类型 | 监控项 | 告警阈值 | 说明 |
|----------|--------|----------|------|
| **归档延迟** | `.ready` 文件数量 | > 10 | 归档积压 |
| **处理延迟** | 切换后处理时间 | > 10分钟 | 补偿处理超时 |
| **成功率** | 补偿成功率 | < 95% | 处理失败率过高 |
| **服务状态** | 归档服务健康状态 | DOWN | 服务不可用 |
| **WAL保留** | WAL文件数量 | < 20 | WAL保留不足 |

### 7.2 故障排查指南

#### 常见问题及解决方案

**问题1：归档积压**
```bash
# 检查命令
find $PGDATA/pg_wal/archive_status -name "*.ready" | wc -l

# 解决方案
# 1. 检查 archive_command 是否正常
# 2. 检查 S3 连接和权限
# 3. 检查磁盘空间
```

**问题2：补偿服务无响应**
```bash
# 检查服务状态
systemctl status trove-archive-continuity

# 检查日志
tail -f /var/log/trove-archive/service.log

# 检查消息队列
rabbitmqctl list_queues name messages
```

**问题3：WAL文件保留不足**
```bash
# 检查WAL保留配置
psql -c "SHOW wal_keep_segments;"
psql -c "SHOW wal_keep_size;"

# 检查WAL文件数量
find $PGDATA/pg_wal -name "[0-9A-F]*" -type f | wc -l
```

**问题4：重复上传**
```bash
# 检查 Redis 锁状态
redis-cli keys "archive_lock:*"

# 检查 S3 重复文件
aws s3 ls s3://trove-wal-archive/instances/{instance_id}/wal/ | sort
```

## 8. 实施计划

### 8.1 开发阶段

| 阶段 | 任务 | 预估工期 | 交付物 |
|------|------|----------|--------|
| **阶段1** | Trove 事件发布集成 | 1周 | 事件发布代码 |
| **阶段2** | 归档连续性服务开发 | 2周 | 独立服务代码 |
| **阶段3** | 冲突协调机制 | 1周 | 协调器代码 |
| **阶段4** | 监控和告警 | 1周 | 监控脚本 |
| **阶段5** | 测试和优化 | 1周 | 测试报告 |

### 8.2 测试验证

#### 测试场景
1. **正常主从切换**：验证归档连续性
2. **网络故障切换**：验证异常情况处理
3. **并发归档冲突**：验证冲突协调机制
4. **服务故障恢复**：验证服务可靠性
5. **WAL保留不足**：验证保留策略有效性

#### 验收标准
- ✅ 主从切换后归档无间隙
- ✅ 正常归档流程无影响
- ✅ 服务故障自动恢复
- ✅ 监控告警正常工作
- ✅ WAL保留策略有效

## 9. 总结与建议

### 9.1 核心结论

1. **openGauss 基于 PostgreSQL 9.2.4**，不支持 `archive_mode=always` 和 `pg_stat_archiver`
2. **Archive Slot 方案复杂度过高**，需要共享存储，不推荐
3. **archive_mode=on + Trove 异步补偿**是最佳方案，平衡了功能需求和实现复杂度
4. **WAL 保留策略至关重要**，必须配置足够的保留时间和空间

### 9.2 技术选型建议

**推荐方案：archive_mode=on + Trove 异步补偿**

**选择理由：**
- ✅ **架构简洁**：无需额外基础设施
- ✅ **功能解耦**：归档逻辑独立于主从切换
- ✅ **运维友好**：基于现有技术栈
- ✅ **风险可控**：故障点少，易于排查
- ✅ **扩展性好**：可适配不同存储后端

### 9.3 关键配置建议

1. **WAL 保留配置**：
   - openGauss: `wal_keep_segments = 128`
   - PostgreSQL 13+: `wal_keep_size = 2GB`
   - 禁用自动清理: `enable_xlog_prune = off`

2. **归档配置**：
   - `archive_mode = on`
   - `archive_timeout = 300`
   - `checkpoint_timeout = 15min`

3. **监控配置**：
   - WAL 文件数量监控
   - 归档延迟监控
   - 服务健康状态监控

### 9.4 风险评估

| 风险项 | 风险等级 | 缓解措施 |
|--------|----------|----------|
| **消息队列故障** | 中 | 消息持久化 + 重试机制 |
| **S3 连接异常** | 中 | 重试 + 降级处理 |
| **WAL保留不足** | 高 | 监控告警 + 自动调整 |
| **服务进程异常** | 低 | 自动重启 + 监控告警 |
| **并发冲突** | 低 | 分布式锁 + 状态检查 |

### 9.5 后续优化方向

1. **性能优化**：批量处理、并发上传
2. **功能增强**：支持更多存储后端
3. **监控完善**：更细粒度的监控指标
4. **自动化**：故障自愈、自动扩容
5. **智能调优**：根据负载自动调整WAL保留策略

---

**文档版本**：v3.0
**最后更新**：2025年7月25日
**调研结论**：推荐 archive_mode=on + Trove 异步补偿方案
**预期收益**：解决归档连续性问题，释放本地磁盘空间，提升系统可靠性
