# WAL 归档连续性实施指南

## 1. 实施概述

本指南提供了基于 `archive_mode=on + Trove 异步补偿` 方案的详细实施步骤，确保 openGauss/PostgreSQL 主从切换时的归档连续性。

## 2. 前置条件

### 2.1 环境要求
- Trove 服务正常运行
- RabbitMQ 消息队列可用
- Redis 服务可用（用于分布式锁）
- S3 兼容的对象存储服务
- Python 3.7+ 环境

### 2.2 权限要求
- Trove 数据库管理权限
- S3 存储桶读写权限
- RabbitMQ 队列操作权限
- Redis 读写权限

## 3. 步骤1：配置 WAL 保留策略

### 3.1 openGauss 配置

```bash
# 编辑 postgresql.conf
vim $PGDATA/postgresql.conf

# 添加或修改以下配置
wal_level = hot_standby
wal_keep_segments = 128          # 保留 128 个 WAL 段（约 2GB）
max_wal_size = 4GB
min_wal_size = 512MB
checkpoint_timeout = 15min
archive_timeout = 300
enable_xlog_prune = off          # 禁用自动 WAL 清理
archive_mode = on
archive_command = '/opt/scripts/opengauss_archive.sh %p %f'

# 重启数据库服务
gs_ctl restart -D $PGDATA
```

### 3.2 PostgreSQL 13.3 配置

```bash
# 编辑 postgresql.conf
vim $PGDATA/postgresql.conf

# 添加或修改以下配置
wal_level = replica
wal_keep_size = 2GB              # 保留 2GB 的 WAL 文件
max_wal_size = 4GB
min_wal_size = 512MB
checkpoint_timeout = 15min
archive_timeout = 300
archive_mode = on
archive_command = '/opt/scripts/postgres_archive.sh %p %f'

# 重新加载配置（无需重启）
SELECT pg_reload_conf();
```

### 3.3 验证配置

```sql
-- 检查配置是否生效
SHOW wal_keep_segments;  -- openGauss
SHOW wal_keep_size;      -- PostgreSQL 13+
SHOW archive_mode;
SHOW archive_command;

-- 检查 WAL 文件数量
SELECT count(*) as wal_file_count 
FROM pg_ls_dir('pg_wal') 
WHERE pg_ls_dir ~ '^[0-9A-F]{24}$';
```

## 4. 步骤2：部署归档连续性服务

### 4.1 创建服务目录

```bash
# 创建服务目录
sudo mkdir -p /opt/trove-archive-continuity
sudo mkdir -p /var/log/trove-archive
sudo mkdir -p /etc/trove-archive

# 设置权限
sudo chown -R trove:trove /opt/trove-archive-continuity
sudo chown -R trove:trove /var/log/trove-archive
sudo chown -R trove:trove /etc/trove-archive
```

### 4.2 安装依赖

```bash
# 安装 Python 依赖
pip3 install oslo.messaging boto3 redis asyncio aiohttp

# 或使用 requirements.txt
cat > /opt/trove-archive-continuity/requirements.txt << EOF
oslo.messaging>=12.0.0
boto3>=1.26.0
redis>=4.0.0
asyncio>=3.4.3
aiohttp>=3.8.0
psycopg2-binary>=2.9.0
EOF

pip3 install -r /opt/trove-archive-continuity/requirements.txt
```

### 4.3 创建配置文件

```bash
cat > /etc/trove-archive/trove-archive-continuity.conf << EOF
[DEFAULT]
debug = True
log_file = /var/log/trove-archive/service.log
log_level = INFO

[oslo_messaging_rabbit]
# 复用 Trove 的 RabbitMQ 配置
rabbit_host = *********
rabbit_port = 5672
rabbit_userid = openstack
rabbit_password = your_rabbit_password
rabbit_virtual_host = /

[archive_continuity]
enabled = true
check_delay_seconds = 60        # 切换后等待时间
max_missing_files = 10          # 最多处理文件数
timeout_seconds = 300           # 处理超时时间
retry_attempts = 3              # 重试次数

[s3_config]
bucket = trove-wal-archive
region = us-east-1
access_key = your_s3_access_key
secret_key = your_s3_secret_key
endpoint_url = https://s3.amazonaws.com

[redis]
host = 127.0.0.1
port = 6379
db = 0
password = your_redis_password

[trove_api]
endpoint = http://trove-api:8779
auth_token_file = /var/lib/trove/auth_token
EOF
```

### 4.4 创建服务主程序

```python
# /opt/trove-archive-continuity/archive_service.py
#!/usr/bin/env python3

import asyncio
import logging
import sys
import time
from oslo_config import cfg
from oslo_messaging import get_notification_listener, Target
import boto3
import redis
from contextlib import contextmanager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ArchiveContinuityService:
    def __init__(self, config_file):
        # 加载配置
        cfg.CONF(args=['--config-file', config_file])
        
        # 初始化客户端
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=cfg.CONF.s3_config.access_key,
            aws_secret_access_key=cfg.CONF.s3_config.secret_key,
            region_name=cfg.CONF.s3_config.region,
            endpoint_url=cfg.CONF.s3_config.endpoint_url
        )
        
        self.redis_client = redis.Redis(
            host=cfg.CONF.redis.host,
            port=cfg.CONF.redis.port,
            db=cfg.CONF.redis.db,
            password=cfg.CONF.redis.password
        )
        
        # 设置消息监听器
        self.setup_listener()
    
    def setup_listener(self):
        """设置消息监听器"""
        targets = [Target(topic='trove.notifications')]
        self.listener = get_notification_listener(
            transport=get_transport(),
            targets=targets,
            endpoints=[self]
        )
    
    def info(self, ctxt, publisher_id, event_type, payload, metadata):
        """处理通知事件"""
        if event_type == 'trove.instance.failover.completed':
            logger.info(f"Received failover event: {payload}")
            
            # 异步处理归档连续性
            asyncio.create_task(
                self.handle_failover_continuity(
                    payload['instance_id'],
                    payload['new_primary_node']
                )
            )
    
    async def handle_failover_continuity(self, instance_id, new_primary_node):
        """处理主从切换的归档连续性"""
        try:
            logger.info(f"Starting archive continuity check for instance {instance_id}")
            
            # 等待切换稳定
            await asyncio.sleep(cfg.CONF.archive_continuity.check_delay_seconds)
            
            # 查找缺失的 WAL 文件
            missing_wals = await self.find_missing_wals(instance_id, new_primary_node)
            
            if missing_wals:
                logger.info(f"Found {len(missing_wals)} missing WAL files")
                await self.upload_missing_wals(instance_id, new_primary_node, missing_wals)
            else:
                logger.info("No missing WAL files found")
                
        except Exception as e:
            logger.error(f"Archive continuity check failed: {e}")
    
    async def find_missing_wals(self, instance_id, new_primary_node):
        """查找缺失的 WAL 文件"""
        # 实现缺失 WAL 检测逻辑
        # 这里是简化版本，实际实现需要调用 Trove API
        return []
    
    async def upload_missing_wals(self, instance_id, node_id, missing_wals):
        """上传缺失的 WAL 文件"""
        for wal_file in missing_wals:
            try:
                with self.archive_lock(instance_id, wal_file) as got_lock:
                    if got_lock:
                        await self.upload_single_wal(instance_id, node_id, wal_file)
                    else:
                        logger.info(f"WAL {wal_file} is being processed by another service")
            except Exception as e:
                logger.error(f"Failed to upload WAL {wal_file}: {e}")
    
    @contextmanager
    def archive_lock(self, instance_id, wal_file):
        """获取归档锁"""
        lock_key = f"archive_lock:{instance_id}:{wal_file}"
        lock_value = f"{time.time()}:{os.getpid()}"
        
        try:
            if self.redis_client.set(lock_key, lock_value, nx=True, ex=300):
                yield True
            else:
                yield False
        finally:
            current_lock = self.redis_client.get(lock_key)
            if current_lock and current_lock.decode() == lock_value:
                self.redis_client.delete(lock_key)
    
    async def upload_single_wal(self, instance_id, node_id, wal_file):
        """上传单个 WAL 文件"""
        # 实现单个文件上传逻辑
        logger.info(f"Uploading WAL file: {wal_file}")
    
    def start(self):
        """启动服务"""
        logger.info("Starting Trove Archive Continuity Service")
        self.listener.start()
        self.listener.wait()

if __name__ == '__main__':
    config_file = sys.argv[1] if len(sys.argv) > 1 else '/etc/trove-archive/trove-archive-continuity.conf'
    service = ArchiveContinuityService(config_file)
    service.start()
```

### 4.5 创建 systemd 服务

```bash
cat > /etc/systemd/system/trove-archive-continuity.service << EOF
[Unit]
Description=Trove Archive Continuity Service
After=network.target rabbitmq-server.service redis.service
Wants=rabbitmq-server.service redis.service

[Service]
Type=simple
User=trove
Group=trove
WorkingDirectory=/opt/trove-archive-continuity
ExecStart=/usr/bin/python3 /opt/trove-archive-continuity/archive_service.py /etc/trove-archive/trove-archive-continuity.conf
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable trove-archive-continuity
sudo systemctl start trove-archive-continuity
```

## 5. 步骤3：集成 Trove 事件发布

### 5.1 修改 Trove Manager

```python
# 在 trove/taskmanager/manager.py 中添加事件发布
from oslo_messaging import notify

class Manager(object):
    def __init__(self):
        # 添加通知器
        self.notifier = notify.Notifier(
            transport=messaging.get_transport(),
            publisher_id='trove.taskmanager',
            topics=['trove.notifications']
        )
    
    def promote_to_replica_source(self, context, instance_id):
        """提升备库为主库（主从切换）"""
        
        try:
            # 获取切换前状态
            old_primary = self._get_current_primary(instance_id)
            
            # 执行原有的切换逻辑
            result = super().promote_to_replica_source(context, instance_id)
            
            # 发布归档事件
            self._publish_failover_event(context, instance_id, old_primary, result)
            
            return result
            
        except Exception as e:
            # 发布失败事件
            self._publish_failover_failed_event(context, instance_id, str(e))
            raise
    
    def _publish_failover_event(self, context, instance_id, old_primary, new_primary):
        """发布主从切换事件"""
        
        event_payload = {
            'instance_id': instance_id,
            'tenant_id': context.tenant,
            'old_primary_node': old_primary.id if old_primary else None,
            'new_primary_node': new_primary.id,
            'failover_timestamp': datetime.utcnow().isoformat(),
            'datastore_type': 'opengauss'  # 或 'postgresql'
        }
        
        self.notifier.info(
            context,
            'trove.instance.failover.completed',
            event_payload
        )
        
        LOG.info(f"Published failover event for instance {instance_id}")
```

## 6. 步骤4：部署监控脚本

### 6.1 创建监控脚本

```bash
cat > /opt/trove-archive-continuity/monitor.sh << 'EOF'
#!/bin/bash
# WAL 归档连续性监控脚本

LOG_FILE="/var/log/trove-archive/monitor.log"
ALERT_THRESHOLD_READY=10
ALERT_THRESHOLD_AGE=60

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

check_service_status() {
    if ! systemctl is-active --quiet trove-archive-continuity; then
        log_message "ALERT: Archive continuity service is not running"
        return 1
    fi
    return 0
}

check_wal_retention() {
    local pgdata="$1"
    
    if [[ ! -d "$pgdata" ]]; then
        return 0
    fi
    
    # 检查 .ready 文件数量
    local ready_count=$(find "$pgdata/pg_wal/archive_status" -name "*.ready" 2>/dev/null | wc -l)
    
    if [[ $ready_count -gt $ALERT_THRESHOLD_READY ]]; then
        log_message "ALERT: High number of .ready files: $ready_count"
    fi
    
    # 检查最老 WAL 文件年龄
    local oldest_wal=$(find "$pgdata/pg_wal" -name "[0-9A-F]*" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | head -1 | cut -d' ' -f2-)
    
    if [[ -n "$oldest_wal" ]]; then
        local oldest_time=$(stat -c %Y "$oldest_wal" 2>/dev/null)
        local current_time=$(date +%s)
        local age_minutes=$(( (current_time - oldest_time) / 60 ))
        
        if [[ $age_minutes -lt $ALERT_THRESHOLD_AGE ]]; then
            log_message "ALERT: WAL retention too short: ${age_minutes} minutes"
        fi
    fi
}

# 主监控循环
main() {
    log_message "Starting archive continuity monitor"
    
    # 检查服务状态
    check_service_status
    
    # 检查所有 PostgreSQL 数据目录
    for pgdata in /var/lib/postgresql/*/data /gaussdb/data; do
        if [[ -d "$pgdata" ]]; then
            check_wal_retention "$pgdata"
        fi
    done
    
    log_message "Monitor check completed"
}

main "$@"
EOF

chmod +x /opt/trove-archive-continuity/monitor.sh
```

### 6.2 设置定时监控

```bash
# 添加 crontab 任务
cat > /etc/cron.d/trove-archive-monitor << EOF
# 每5分钟检查一次归档状态
*/5 * * * * trove /opt/trove-archive-continuity/monitor.sh
EOF
```

## 7. 步骤5：测试验证

### 7.1 功能测试

```bash
# 1. 测试服务启动
sudo systemctl status trove-archive-continuity

# 2. 测试日志输出
tail -f /var/log/trove-archive/service.log

# 3. 测试 Redis 连接
redis-cli ping

# 4. 测试 S3 连接
aws s3 ls s3://trove-wal-archive/
```

### 7.2 模拟主从切换测试

```bash
# 1. 创建测试实例
trove create test-instance ...

# 2. 触发主从切换
trove promote-to-replica-source <replica-id>

# 3. 检查事件是否发布
rabbitmqctl list_queues name messages

# 4. 检查归档连续性服务日志
tail -f /var/log/trove-archive/service.log
```

## 8. 故障排查

### 8.1 常见问题

**问题1：服务启动失败**
```bash
# 检查配置文件
sudo -u trove python3 -c "from oslo_config import cfg; cfg.CONF(['--config-file', '/etc/trove-archive/trove-archive-continuity.conf'])"

# 检查依赖
pip3 list | grep oslo.messaging
```

**问题2：无法接收事件**
```bash
# 检查 RabbitMQ 连接
rabbitmqctl list_connections
rabbitmqctl list_queues name messages
```

**问题3：S3 上传失败**
```bash
# 测试 S3 连接
aws s3 ls s3://trove-wal-archive/ --region us-east-1
```

### 8.2 日志分析

```bash
# 查看服务日志
journalctl -u trove-archive-continuity -f

# 查看应用日志
tail -f /var/log/trove-archive/service.log

# 查看监控日志
tail -f /var/log/trove-archive/monitor.log
```

## 9. 维护和优化

### 9.1 定期维护任务

```bash
# 1. 清理过期日志
find /var/log/trove-archive/ -name "*.log" -mtime +30 -delete

# 2. 检查 Redis 内存使用
redis-cli info memory

# 3. 检查 S3 存储使用
aws s3api list-objects-v2 --bucket trove-wal-archive --query 'sum(Contents[].Size)'
```

### 9.2 性能优化

```bash
# 1. 调整 WAL 保留策略
# 根据实际负载调整 wal_keep_segments

# 2. 优化上传并发
# 在配置中调整 max_missing_files 参数

# 3. 监控系统资源
htop
iotop
```

---

**实施指南版本**：v1.0  
**适用环境**：openGauss/PostgreSQL + Trove  
**最后更新**：2025年7月25日
