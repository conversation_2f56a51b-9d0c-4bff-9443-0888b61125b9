# WAL 归档连续性解决方案文档

本目录包含了 openGauss/PostgreSQL WAL 归档连续性解决方案的完整技术文档。

## 文档结构

### 📋 [调研报告](./opengauss-postgresql-wal-archive-continuity-research.md)
**主要内容：**
- 技术背景和版本差异分析
- 归档机制深度解析（源码级别）
- 方案对比和技术选型
- WAL 保留策略配置
- 架构设计和实现方案

**适合读者：** 技术决策者、架构师、高级开发工程师

### 🛠️ [实施指南](./wal-archive-continuity-implementation-guide.md)
**主要内容：**
- 详细的部署步骤
- 配置文件模板
- 代码实现示例
- 测试验证方法
- 故障排查指南

**适合读者：** 运维工程师、开发工程师、系统管理员

### ⚡ [快速参考](./wal-archive-quick-reference.md)
**主要内容：**
- 核心配置参数
- 常用命令和检查脚本
- 监控指标和告警配置
- 故障排查快速诊断
- 性能调优建议

**适合读者：** 运维人员、值班工程师

## 解决方案概述

### 问题背景
在 Trove 管理的 openGauss/PostgreSQL 主从环境中，当发生主从切换时，可能出现 WAL 归档间隙，导致：
- 数据完整性风险
- 备份恢复困难
- 合规性问题

### 解决方案
采用 **archive_mode=on + Trove 异步补偿** 方案：

```
主从切换触发 → Trove 发布事件 → 归档服务异步处理 → 检测并补偿缺失的 WAL 文件
```

### 核心优势
- ✅ **架构简洁**：无需额外基础设施
- ✅ **功能解耦**：归档逻辑独立于主从切换
- ✅ **运维友好**：基于现有技术栈
- ✅ **风险可控**：故障点少，易于排查

## 快速开始

### 1. 配置 WAL 保留策略

**openGauss:**
```sql
wal_keep_segments = 128          -- 保留 2GB WAL
enable_xlog_prune = off          -- 禁用自动清理
archive_mode = on
```

**PostgreSQL 13.3:**
```sql
wal_keep_size = 2GB              -- 保留 2GB WAL
archive_mode = on
```

### 2. 部署归档连续性服务

```bash
# 安装依赖
pip3 install oslo.messaging boto3 redis

# 创建配置文件
cp /etc/trove-archive/trove-archive-continuity.conf.example \
   /etc/trove-archive/trove-archive-continuity.conf

# 启动服务
systemctl enable trove-archive-continuity
systemctl start trove-archive-continuity
```

### 3. 验证部署

```bash
# 检查服务状态
systemctl status trove-archive-continuity

# 检查 WAL 保留
find $PGDATA/pg_wal -name "[0-9A-F]*" -type f | wc -l

# 检查归档状态
find $PGDATA/pg_wal/archive_status -name "*.ready" | wc -l
```

## 技术架构

### 组件关系图
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Waltz    │───▶│    Trove    │───▶│  RabbitMQ   │
│   监控服务   │    │   控制层    │    │   消息队列   │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     S3      │◀───│   Archive   │◀───│   Archive   │
│   对象存储   │    │   Service   │    │  Listener   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 数据流程
1. **正常归档**：主库 → archive_command → S3
2. **主从切换**：Waltz → Trove → RabbitMQ → Archive Service
3. **间隙补偿**：Archive Service → 新主库 → S3

## 监控和告警

### 关键指标
| 指标 | 正常值 | 告警阈值 |
|------|--------|----------|
| WAL 文件数量 | > 20 | < 10 |
| 归档积压 | < 5 | > 10 |
| 服务状态 | Running | Stopped |

### 监控命令
```bash
# 健康检查
/opt/trove-archive-continuity/monitor.sh

# 服务状态
systemctl status trove-archive-continuity

# 日志查看
journalctl -u trove-archive-continuity -f
```

## 版本兼容性

| 数据库版本 | archive_mode 支持 | 推荐方案 |
|------------|-------------------|----------|
| PostgreSQL 9.2.4 | on/off | ✅ 本方案 |
| PostgreSQL 13.3 | on/off/always | ✅ 本方案 或 always |
| openGauss 3.x | on/off | ✅ 本方案 |

## 故障排查

### 常见问题
1. **归档积压** → 检查 archive_command 和 S3 连接
2. **WAL 保留不足** → 增加 wal_keep_segments/wal_keep_size
3. **服务无响应** → 重启服务，检查配置和依赖

### 诊断工具
- 健康检查脚本：`monitor.sh`
- 日志分析：`journalctl -u trove-archive-continuity`
- 配置验证：SQL 查询命令

## 性能调优

### 负载分级配置

**高负载（WAL > 100MB/min）**
```sql
wal_keep_segments = 256      -- 4GB
max_wal_size = 8GB
```

**中等负载（WAL 50-100MB/min）**
```sql
wal_keep_segments = 128      -- 2GB
max_wal_size = 4GB
```

**低负载（WAL < 50MB/min）**
```sql
wal_keep_segments = 64       -- 1GB
max_wal_size = 2GB
```

## 安全考虑

### 权限配置
- S3 存储桶访问权限
- RabbitMQ 队列操作权限
- Redis 访问权限
- 数据库管理权限

### 数据保护
- WAL 文件传输加密
- S3 存储加密
- 访问日志记录

## 贡献指南

### 文档更新
1. 修改相应的 Markdown 文件
2. 更新版本号和修改日期
3. 验证链接和格式
4. 提交 Pull Request

### 代码贡献
1. 遵循现有代码风格
2. 添加必要的测试
3. 更新相关文档
4. 通过 Code Review

## 支持和反馈

### 技术支持
- 查看 [故障排查指南](./wal-archive-continuity-implementation-guide.md#8-故障排查)
- 检查 [常见问题](./wal-archive-quick-reference.md#故障排查)

### 问题反馈
- 技术问题：提交 Issue 并附上详细日志
- 文档问题：直接提交 Pull Request
- 功能建议：在 Issue 中详细描述需求

## 更新日志

### v3.0 (2025-07-25)
- 完善 WAL 保留策略配置
- 增加性能调优建议
- 优化故障排查流程
- 添加监控告警配置

### v2.0 (2025-07-25)
- 完成方案技术选型
- 确定推荐架构方案
- 添加详细实施指南

### v1.0 (2025-07-25)
- 初始版本发布
- 完成技术调研
- 确定解决方案方向

---

**文档维护者**：数据库团队  
**最后更新**：2025年7月25日  
**文档版本**：v3.0
