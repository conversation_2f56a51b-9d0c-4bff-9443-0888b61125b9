# WAL 归档连续性快速参考

## 核心配置

### openGauss 配置
```sql
-- postgresql.conf 关键配置
wal_level = hot_standby
wal_keep_segments = 128          -- 保留 2GB WAL
max_wal_size = 4GB
checkpoint_timeout = 15min
archive_timeout = 300
enable_xlog_prune = off          -- 禁用自动清理
archive_mode = on
```

### PostgreSQL 13.3 配置
```sql
-- postgresql.conf 关键配置
wal_level = replica
wal_keep_size = 2GB              -- 保留 2GB WAL
max_wal_size = 4GB
checkpoint_timeout = 15min
archive_timeout = 300
archive_mode = on
```

## 关键命令

### 检查 WAL 状态
```bash
# 检查 WAL 文件数量
find $PGDATA/pg_wal -name "[0-9A-F]*" -type f | wc -l

# 检查归档积压
find $PGDATA/pg_wal/archive_status -name "*.ready" | wc -l

# 检查 WAL 目录大小
du -sh $PGDATA/pg_wal
```

### 检查配置
```sql
-- 检查关键参数
SHOW wal_keep_segments;  -- openGauss
SHOW wal_keep_size;      -- PostgreSQL 13+
SHOW archive_mode;
SHOW archive_command;
SHOW checkpoint_timeout;
```

### 服务管理
```bash
# 启动归档连续性服务
sudo systemctl start trove-archive-continuity

# 检查服务状态
sudo systemctl status trove-archive-continuity

# 查看服务日志
journalctl -u trove-archive-continuity -f
```

## 监控检查

### 关键指标
| 指标 | 正常范围 | 告警阈值 |
|------|----------|----------|
| WAL 文件数量 | > 20 | < 10 |
| .ready 文件数量 | < 5 | > 10 |
| WAL 保留时间 | > 2小时 | < 1小时 |
| 归档延迟 | < 5分钟 | > 10分钟 |

### 快速检查脚本
```bash
#!/bin/bash
# 快速健康检查
echo "=== WAL Archive Health Check ==="

# 检查服务状态
systemctl is-active trove-archive-continuity && echo "✅ Service: Running" || echo "❌ Service: Stopped"

# 检查 WAL 文件数量
wal_count=$(find /var/lib/postgresql/*/data/pg_wal -name "[0-9A-F]*" 2>/dev/null | wc -l)
echo "WAL files: $wal_count"

# 检查归档积压
ready_count=$(find /var/lib/postgresql/*/data/pg_wal/archive_status -name "*.ready" 2>/dev/null | wc -l)
echo "Ready files: $ready_count"

# 检查 Redis 连接
redis-cli ping > /dev/null && echo "✅ Redis: Connected" || echo "❌ Redis: Disconnected"
```

## 故障排查

### 常见问题快速诊断

**问题：归档积压**
```bash
# 检查
find $PGDATA/pg_wal/archive_status -name "*.ready" | wc -l

# 解决
# 1. 检查 archive_command
# 2. 检查 S3 连接
# 3. 检查磁盘空间
```

**问题：WAL 保留不足**
```bash
# 检查
find $PGDATA/pg_wal -name "[0-9A-F]*" -type f | wc -l

# 解决
# 增加 wal_keep_segments 或 wal_keep_size
```

**问题：服务无响应**
```bash
# 检查
systemctl status trove-archive-continuity
journalctl -u trove-archive-continuity --since "1 hour ago"

# 解决
sudo systemctl restart trove-archive-continuity
```

## 配置模板

### 生产环境配置
```ini
# /etc/trove-archive/trove-archive-continuity.conf
[DEFAULT]
debug = False
log_file = /var/log/trove-archive/service.log

[archive_continuity]
enabled = true
check_delay_seconds = 60
max_missing_files = 10
timeout_seconds = 300

[s3_config]
bucket = trove-wal-archive
region = us-east-1

[redis]
host = 127.0.0.1
port = 6379
db = 0
```

### 测试环境配置
```ini
# /etc/trove-archive/trove-archive-continuity.conf
[DEFAULT]
debug = True
log_file = /var/log/trove-archive/service.log

[archive_continuity]
enabled = true
check_delay_seconds = 30
max_missing_files = 5
timeout_seconds = 180
```

## 性能调优

### 根据负载调整配置

**高负载环境（WAL > 100MB/min）**
```sql
wal_keep_segments = 256      -- 4GB
max_wal_size = 8GB
checkpoint_timeout = 10min
```

**中等负载环境（WAL 50-100MB/min）**
```sql
wal_keep_segments = 128      -- 2GB
max_wal_size = 4GB
checkpoint_timeout = 15min
```

**低负载环境（WAL < 50MB/min）**
```sql
wal_keep_segments = 64       -- 1GB
max_wal_size = 2GB
checkpoint_timeout = 20min
```

## 告警配置

### Prometheus 监控指标
```yaml
# WAL 文件数量
wal_files_count{instance="$instance"} < 20

# 归档积压
archive_ready_files{instance="$instance"} > 10

# 服务状态
up{job="trove-archive-continuity"} == 0
```

### 告警规则示例
```yaml
groups:
- name: wal_archive_alerts
  rules:
  - alert: WALRetentionLow
    expr: wal_files_count < 20
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "WAL retention is low"
      
  - alert: ArchiveBacklog
    expr: archive_ready_files > 10
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Archive backlog detected"
```

## 版本兼容性

### 支持的版本
| 数据库 | 版本 | archive_mode | pg_stat_archiver | Archive Slot |
|--------|------|--------------|------------------|--------------|
| PostgreSQL | 9.2.4 | on/off | ❌ | ❌ |
| PostgreSQL | 13.3 | on/off/always | ✅ | ❌ |
| openGauss | 3.x | on/off | ❌ | ✅ |

### 功能对比
| 功能 | PostgreSQL 9.2 | PostgreSQL 13 | openGauss |
|------|-----------------|---------------|-----------|
| 基础归档 | ✅ | ✅ | ✅ |
| 备库归档 | ❌ | ✅ (always) | ❌ |
| 归档监控 | 文件系统 | pg_stat_archiver | 文件系统 |
| 高级功能 | ❌ | 标准SQL | Archive Slot |

## 最佳实践

### 配置建议
1. **WAL 保留**：至少保留 2 小时的 WAL 文件
2. **监控频率**：每 5 分钟检查一次归档状态
3. **告警阈值**：.ready 文件超过 10 个时告警
4. **备份验证**：定期验证 S3 中的归档文件完整性

### 运维建议
1. **定期检查**：每日检查归档服务状态
2. **容量规划**：根据 WAL 生成速度规划存储容量
3. **故障演练**：定期进行主从切换演练
4. **文档更新**：及时更新配置和流程文档

---

**快速参考版本**：v1.0  
**最后更新**：2025年7月25日
