PostgreSQL 7.3 multi-byte (MB) support README	       2002/10/21 $B:n@.(B

							$B@P0fC#IW(B
						<EMAIL>

$B"#$O$8$a$K(B

  PostgreSQL $B$K$*$1$k%^%k%A%P%$%H%5%]!<%H$O0J2<$N$h$&$JFCD'$r;}$C$F$$$^$9!%(B

    1. $B%^%k%A%P%$%HJ8;z$H$7$F!$F|K\8l!$Cf9q8l$J$I$N3F9q$N(B EUC$B!$(BUnicode$B!$(B
       mule internal code, ISO-8859-* $B$,%G!<%?%Y!<%9:n@.;~$KA*Br2DG=!%(B
       $B%G!<%?%Y!<%9$K$O$3$N%(%s%3!<%G%#%s%0$N$^$^3JG<$5$l$^$9!%(B
    2. $B%F!<%V%kL>$K%^%k%A%P%$%HJ8;z$,;HMQ2DG=(B
    3. $B%+%i%`L>$K%^%k%A%P%$%HJ8;z$,;HMQ2DG=(B
    4. $B%G!<%?$=$N$b$N$K$b%^%k%A%P%$%HJ8;z$,;HMQ2DG=(B
    5. $B%^%k%A%P%$%HJ8;z$N@55,I=8=8!:w$,;HMQ2DG=(B
    6. $B%^%k%A%P%$%HJ8;z$N(B LIKE $B8!:w$,;HMQ2DG=(B
    7. character_length(), position(), substring() $B$J$I$NJ8;zNs4X?t$G(B
       $B$N%^%k%A%P%$%H%5%]!<%H(B
    8. $B%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0$,%P%C%/%(%s%IB&$H0[$k>l9g$K!$(B
       $B<+F0E*$K%(%s%3!<%G%#%s%0JQ49$r9T$J$$$^$9!%(B
    9. $B%f!<%6Dj5A$N%(%s%3!<%G%#%s%0JQ49$r:n@.2DG=!%(B

  $B%^%k%A%P%$%H%5%]!<%H$,07$&$3$H$N$G$-$k%(%s%3!<%G%#%s%0$O0J2<$K$J$j$^(B
  $B$9!%(B

	SQL_ASCII	ASCII
	EUC_JP		$BF|K\8l(B EUC
	EUC_CN		GB $B$r%Y!<%9$K$7$?CfJ8(BEUC$B!%(Bcode set 2 $B$O(B
			SS2+2$B%P%$%H%3!<%I(B = 3$B%P%$%HI=8=$G$9!%(B
	EUC_KR		$B4Z9q8l(B EUC$B!%(B
	JOHAB		$B%O%s%0%k%Y!<%9$N4Z9q8l(BEUC.
	EUC_TW		$BBfOQ$N(B EUC$B!%(Bcode set 2 $B$O(B
			SS2+$BLLHV9f(B+2$B%P%$%H%3!<%I(B = 4$B%P%$%HI=8=$G$9!%(B
	UNICODE		UTF-8$B!%$?$@$7%5%]!<%H$9$k$N$O(B UCS-2 $B$NHO0O!$(B
			$B$9$J$o$A(B 0xffff $B$^$G$G$9!%(B
	MULE_INTERNAL	mule $B$NFbIt%3!<%I!%$?$@$7!$(BType N $B$NITDjD9J8;z$O(B
			$B%5%]!<%H$7$F$$$^$;$s!%(B
	LATIN1 $B$+$i(B LATIN10$B$^$G(B
	ISO_8859_1 $B$+$i(B 16$B$^$G(B
	$B%-%j%kJ8;z(B	KOI8(KOI8-R), WIN(CP1251), ALT(CP866)$B$r%5%]!<%H(B
			$B$7$F$$$^$9!%$b$A$m$s(B ISO 8859-5 $B$b;HMQ2DG=$G$9!%(B
			$B$3$N>l9g!$(B"LATIN5" $B$H$7$F;XDj$7$F2<$5$$!%(B
	WIN1256		$B%"%i%V=t9q8l(BWindows$BMQ%(%s%3!<%G%#%s%0(B.
	TCVN		$B%Y%H%J%`8l(B."ABC"$B$d(B"VSCII"$B$b;HMQ2DG=(B.
	WIN874		$B%?%$8l(B.

  $B%U%m%s%H%(%s%IB&$G$O$5$i$K0J2<$N%(%s%3!<%G%#%s%0$,;HMQ$G$-$^$9!%(B

	SJIS		$B%7%U%H(BJIS(MS932$B$H$[$\8_49(B)
	BIG5		$BBfOQ$d9a9A$G;HMQ$5$l$F$$$kCf9q8l!%(BEUC_TW$B$H8_49(B
			$B@-$,$"$j$^$9!%(B
	GBK		Windows-936
	UHC		Windows-949
	WIN1250		Windows-1250
	GB18030		GB18030

$B"#F|K\8l$r;HMQ$9$k$3$H$N$G$-$k%(%s%3!<%G%#%s%0(B

  $BA*Br$NL\0B$H$7$F$O!$1Q8l$HF|K\8l$7$+;H$o$J$$>l9g$O(B EUC_JP($BF1MM$K!$Cf(B
  $B9q8l$7$+;H$o$J$$>l9g$O(B EUC_CN... $B$J$I$H$J$j$^$9(B)$B!$$=$NB>$N8@8l$b;H$$$?(B
  $B$$>l9g$O(B UNICODE $B$b$7$/$O(B MULE_INTERNAL $B$H$J$k$G$7$g$&!%(B

  $BCm0U!'(BMULE_INTERNAL $B$rA*$V$H!$$?$/$5$s$NJ8;z=89g$KBP1~$G$-$FJXMx$G$9(B
  $B$,!$@55,I=8=$GJ#?t$NJ8;z=89g$K$^$?$,$k$h$&$JHO0O;XDj(B($B$?$H$($P!$(B[a-$BHO(B]
  $B$H$+!$(B[abc$BHO0O(B]$B$N$h$&$J(B)$B$O;H$($^$;$s!%J#?t$NHO0O;XDj$G0[$J$kJ8;z=89g(B
  $B$r;H$&$N$O9=$$$^$;$s(B($B$?$H$($P(B [abc][$BHO(B-$B0O(B])$B!%$^$?!$(B[^a] $B$N$h$&$JI=8=(B
  $B$O!$(B"a" $B$NB0$9$kJ8;z=89g(B($B$3$N>l9g!$(BUS-ASCII)$B$K$*$$$F(B "a" $B0J30$G$"$k(B
  $B$3$H$rI=$7$^$9!%7h$7$F4A;z$dJ?2>L>$J$I(B "a" $B0J30$r$9$Y$FI=$9$o$1$G$O(B
  $B$J$$$3$H$KCm0U$7$F2<$5$$!%(B

$B"#%$%s%9%H!<%k(B

  PostgreSQL 7.3$B$+$i$O(Bconfigure$B$N%*%W%7%g%s;XDj$NM-L5$K4X$o$i$:!$%^%k(B
  $B%A%P%$%H%5%]!<%H$,M-8z$K$J$C$F$$$^$9$N$G!$FC$K(Bconifgure$B;~$K%^%k%A%P(B
  $B%$%HMQ$NFCJL$J%*%W%7%g%s$r;XDj$9$kI,MW$O$"$j$^$;$s!%(B

$B"#(Binitdb/createdb/create database $B$K$*$1$k%(%s%3!<%G%#%s%0$N;XDj$K$D$$$F(B

  initdb $B$G$O0J2<$N%*%W%7%g%s$G%(%s%3!<%G%#%s%0$,;XDj$G$-$^$9!%(B

	-E $B%(%s%3!<%G%#%s%0(B
	--encoding=$B%(%s%3!<%G%#%s%0(B

  $B$3$3$G;XDj$7$?%(%s%3!<%G%#%s%0$O!$0J8e(B createdb/create database $B$G%((B
  $B%s%3!<%G%#%s%0$r>JN,$7$?>l9g$K@_Dj$5$l$k%(%s%3!<%G%#%s%0$K$J$j$^$9!%(B
  -E $B$^$?$O(B --encoding $B%*%W%7%g%s$r>JN,$7$?>l9g$O!$%(%s%3!<%G%#%s%0$H(B
  $B$7$F(BSQL_ASCII$B$,:NMQ$5$l$F$7$^$&$N$G!$F|K\8l$r%G%U%)%k%H$G;HMQ$9$k>l(B
  $B9g$O!$(B

	-E EUC_JP

   $B$"$k$$$O(B

	--encoding=EUC_JP

  $B$H$7$FI,$:L@<(E*$K%(%s%3!<%G%#%s%0$r;XDj$7$F$/$@$5$$!%(B

  $B$J$*!$(BPostgreSQL 7.3$B0J9_%m%1!<%k%5%]!<%H$,I,$:M-8z$K$J$C$F$$$^$9$,!$(B
  $B$3$l$OF|K\8l$J$I$r;HMQ$9$k:]$K$O2?$N%a%C%j%H$b$J$$$P$+$j$G$J$/!$>c32(B
  $B$N860x$K$J$C$?$j!$(BLIKE$B8!:w$d@55,I=8=8!:w$G%$%s%G%C%/%9$,M-8z$K$J$i$J(B
  $B$$$J$I$NLdBj$r0z$-5/$3$9$N$G!$L58z$K$7$F$*$/$3$H$r$*$9$9$a$7$^$9!%%m(B
  $B%1!<%k%5%]!<%H$rL58z$K$9$k$?$a$K$O!$(B

	--no-locale

  $B%*%W%7%g%s$r;XDj$7$^$9!%(B

  createdb $B$G$O0J2<$N%*%W%7%g%s$G%(%s%3!<%G%#%s%0$,;XDj$G$-$^$9!%(B

	-E $B%(%s%3!<%G%#%s%0(B
	--encoding=$B%(%s%3!<%G%#%s%0(B

  create database $B$G$O0J2<$N%*%W%7%g%s$G%(%s%3!<%G%#%s%0$,;XDj$G$-$^$9!%(B

	CREATE DATABASE dbanme WITH ENCODING = '$B%(%s%3!<%G%#%s%0(B';

  LOCATION $B$rF1;~$K;XDj$9$k>l9g$O0J2<$N$h$&$K$J$j$^$9!%(B

	CREATE DATABASE dbanme WITH LOCATION = 'path' ENCODING = '$B%(%s%3!<%G%#%s%0(B';

  createdb/create database $B$G$O!$%(%s%3!<%G%#%s%0;XDj$r>JN,$7$?>l9g$O!$(Binitdb 
  $B$G;XDj$7$?%(%s%3!<%G%#%s%0$,:NMQ$5$l$^$9!%$3$l$O!$(Binitdb $B$,:n@.$9$k(B
  $B%F%s%W%l!<%H%G!<%?%Y!<%9(B(template1)$B$N(B encoding $B%"%H%j%S%e!<%H$r7Q>5(B
  $B$9$k$+$i$G$9!%(B

  $B%G!<%?%Y!<%9$N%(%s%3!<%G%#%s%0$O!$(Bpsql -l$B!$(Bpsql $B$N(B \l $B$G;2>H$G$-$^$9!%(B

$ psql -l
            List of databases
   Database    |  Owner  |   Encoding    
---------------+---------+---------------
 euc_cn        | t-ishii | EUC_CN
 euc_jp        | t-ishii | EUC_JP
 euc_kr        | t-ishii | EUC_KR
 euc_tw        | t-ishii | EUC_TW
 mule_internal | t-ishii | MULE_INTERNAL
 regression    | t-ishii | SQL_ASCII
 template1     | t-ishii | EUC_JP
 test          | t-ishii | EUC_JP
 unicode       | t-ishii | UNICODE
(9 rows)

$B"#J8;z7?$N%G!<%?7?$K$D$$$F(B

  7.2$B$G$O!$(BCHAR(n)$B$H(BVARCHAR(n)$B$N(B n $B$OJ8;z?t$r0UL#$7$^$9!%(Bn $B$,%P%$%H?t$r(B
  $B0UL#$9$k(B 7.1 $B0JA0$H$O0[$J$j$^$9$N$G$4Cm0U2<$5$$!%(B

  $BNc$r<($7$^$9!%(B

  7.2$B$G$O!$(BCHAR(1)$B$K(B"$B$"(B"$B$r3JG<$G$-$^$9$,!$(B7.1$B0JA0$G$O3JG<$G$-$^$;$s$3(B
  $B$l$O!$(B"$B$"(B"$B$r3JG<$9$k$?$a$K>/$J$/$H$b(B2$B%P%$%H0J>e$rMW$9$k$+$i$G$9!%(B
  $B5U$K!$(B"a" $B$O(B1$B%P%$%H$7$+>CHq$7$J$$$?$a!$(B7.1$B$G$b(B CHAR(1) $B$K3JG<$G$-$^(B
  $B$9!%(B

  $B$J$*!$(B7.2$B$G$O!$(B7.1$B$^$G$H0[$J$j!$(BCHAR(n)$B$K3JG<$G$-$J$$(B n $BJ8;z$h$jBg$-(B
  $B$$J8;zNs$O(B n $BJ8;z$G@Z$j<N$F$i$l$k$N$G$O$J$/!$%(%i!<$K$J$k$3$H$K$4Cm(B
  $B0U2<$5$$!%$3$l$O!$%^%k%A%P%$%HBP1~$NM-L5$K4X$o$i$:!$J8;zNs$N07$$$,(B
  SQL$BI8=`$K1h$&$h$&$KJQ$C$?$+$i$G$9!%(B

$B"#%U%m%s%H%(%s%I$H%P%C%/%(%s%I$N<+F0%(%s%3!<%G%#%s%0JQ49$K$D$$$F(B

  $B%P%C%/%(%s%I(B($B%G!<%?%Y!<%9(B)$B$H(B psql $B$J$I$N%U%m%s%H%(%s%I$N%(%s%3!<%G%#(B
  $B%s%0$O0lCW$7$F$$$k$N$,86B'$G$9$,!$$$$/$D$+$N%(%s%3!<%G%#%s%0$K$D$$$F(B
  $B$O%P%C%/%(%s%I$H%U%m%s%H%(%s%I$N4V$G0[$J$k$b$N$r;HMQ$9$k$3$H$,$G$-$^(B
  $B$9!%$3$N>l9g!$<+F0E*$K%P%C%/%(%s%I$G%(%s%3!<%G%#%s%0JQ49$,9T$o$l$^$9!%(B

  $B%P%C%/%(%s%I$N%(%s%3!<%G%#%s%0(B	$B5vMF$5$l$k%U%m%s%H%(%s%I$N(B
					$B%(%s%3!<%G%#%s%0(B
  ----------------------------------------------------------------
	EUC_JP				EUC_JP, SJIS, UNICODE

	EUC_TW				EUC_TW, BIG5, UNICODE

	EUC_CN				EUC_CN, UNICODE

	EUC_KR				EUC_KR, UNICODE

	JOHAB				JOHAB, UNICODE

	LATIN1,3,4			LATIN1,3,4, UNICODE

  	LATIN2				LATIN2, WIN1250, UNICODE

	LATIN5				LATIN5, WIN, ALT, UNICODE

	LATIN6,7,8,9,10			LATIN6,7,8,9,10, UNICODE

	ISO_8859_5,6,7,8		ISO_8859_5,6,7,8, UNICODE

	WIN1256				WIN1256, UNICODE

	TCVN				TCVN, UNICODE

	WIN874				WIN874, UNICODE

	MULE_INTERNAL			EUC_JP, SJIS, EUC_KR, EUC_CN, 
					EUC_TW, BIG5, LATIN1$B$+$i(B5, 
					WIN, ALT, WIN1250

	UNICODE				EUC_JP, SJIS, EUC_KR, UHC,
					EUC_CN, GBK, EUC_TW, BIG5,
					LATIN1$B$+$i(B10, ISO_8859_5$B$+$i(B8,
					WIN, ALT, WIN1250, WIN1256,
					TCVN, WIN874, JOHAB
  ----------------------------------------------------------------

  $B%P%C%/%(%s%I$H%U%m%s%H%(%s%I$N%(%s%3!<%G%#%s%0$,0[$J$k>l9g!$$=$N$3$H(B
  $B$r%P%C%/%(%s%I$KEA$($kI,MW$,$"$j$^$9!%$=$N$?$a$NJ}K!$,$$$/$D$+$"$j$^(B
  $B$9!%(B

o psql $B$N(B \encoding $B%3%^%s%I$r;H$&J}K!(B

  psql$B$G$O!$(B\encoding$B%3%^%s%I$r;H$C$FF0E*$K%U%m%s%H%(%s%IB&$NJ8;z%3!<(B
  $B%I$r@ZBX$($k$3$H$,$G$-$^$9!%Nc(B:

	\encoding SJIS

o libpq $B$N4X?t(B PQsetClientEncoding $B$r;H$&J}K!(B

  7.0 $B$+$i?7$7$$(B libpq $B4X?t(B PQsetClientEncoding $B$,DI2C$5$l$F$$$^$9!%(B

  PQsetClientEncoding(PGconn *conn, const char *encoding)

  $B$3$N4X?t$r;H$($P!$%3%M%/%7%g%sKh$K%(%s%3!<%G%#%s%0$r@ZBX$($k$3$H$,$G(B
  $B$-$^$9!%8=:_$N%(%s%3!<%G%#%s%0$NLd$$9g$o$;$O(B

  int PQclientEncoding(const PGconn *conn)

  $B$G$9!%(B

o postgresql.conf $B$G@_Dj$9$kJ}K!(B

  $B%U%m%s%H%(%s%I$N%G%U%)%k%H%(%s%3!<%G%#%s%0$r;XDj$9$k$K$O!$(B
  postgresql.conf $B$N(B client_encoding $B$r;XDj$7$^$9!%;XDjNc(B:

  client_encoding = SJIS

o $B4D6-JQ?t(B PGCLIENTENCODING $B$r;H$&J}K!(B

  (1) postmaster $B5/F0;~$K4D6-JQ?t$r@_Dj$9$kJ}K!(B

  $B4D6-JQ?t(B PGCLIENTENCODING $B$r@_Dj$9$k$3$H$K$h$j!$(B postgresql.conf $B$G(B
  $B%(%s%3!<%G%#%s%0$r;XDj$9$k$N$HF1$88z2L$,F@$i$l$^$9!%$?$@$7!$$3$l$ONr(B
  $B;KE*7P0^$+$i;D$5$l$F$$$k5!G=$G!$:#8e$O$3$N5!G=$rMxMQ$7$J$$$3$H$r$*$9(B
  $B$9$a$7$^$9!%@_DjNc(B:

  export PGCLIENTENCODING=SJIS postmaster -S

  (2) $B%/%i%$%"%s%H!$%U%m%s%H%(%s%IKh$K%(%s%3!<%G%#%s%0$r@_Dj$7$?$$>l9g(B

  $B$=$N%U%m%s%H%(%s%I(B($B$?$H$($P(B psql)$B$r5/F0$9$kA0$K4D6-JQ?t(B 
  PGCLIENTENCODING $B$r@_Dj$7$^$9!%(B

o set client_encoding $B%3%^%s%I$r;H$&J}K!(B

  SET CLIENT_ENCODING SQL$B%3%^%s%I$r;H$C$FF0E*$K%U%m%s%H%(%s%I$N%(%s%3!<(B
  $B%G%#%s%0$rJQ99$G$-$^$9!%Nc(B:

	SET CLIENT_ENCODING TO SJIS;

$B"#8=:_@_Dj$5$l$F$$$k%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0$rD4$Y$k(B

 $B8=:_@_Dj$5$l$F$$$k%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0$O(B

	show client_encoding;

 $B$G;2>H$G$-$^$9(B($B>.J8;z$GI=<($5$l$^$9(B)$B!%(B

$B"#%G%U%)%k%H$N%(%s%3!<%G%#%s%0$X$NI|5"(B

  SQL$B%3%^%s%I(B:

	RESET CLIENT_ENCODING;

  $B$O!$%G%U%)%k%H$N%U%m%s%H%(%s%I%(%s%3!<%G%#%s%0@_Dj$KI|5"$5$;$^$9!%(B
  postmaster$B$rN)$A>e$2$k$H$-$K(B postgresql.conf $B$N(B client_encoding $B$d4D(B
  $B6-JQ?t(B PGCLIENTENCODING $B$,@_Dj$5$l$F$$$k$H$=$N%(%s%3!<%G%#%s%0$K!$$=(B
  $B$&$G$J$1$l$P%G!<%?%Y!<%9$N%(%s%3!<%G%#%s%0$HF1$8$K$J$j$^$9!%(B
  
$B"#L@<(E*$J%(%s%3!<%G%#%s%0JQ49(B

  7.2$B$G$O!$(Bconvert$B$H$$$&4X?t$r;H$$!$L@<(E*$J%(%s%3!<%G%#%s%0JQ49$,$G$-(B
  $B$^$9!%(B

  convert(string text, [src_encoding name,] dest_encoding name) 

  $B$3$3$G(Bsrc_encoding$B$O(Btext$B$N%(%s%3!<%G%#%s%0L>$G$9!%>JN,$9$k$H!$%G!<%?(B
  $B%Y!<%9%(%s%3!<%G%#%s%0L>$HF1$8$G$"$k$H8+$J$5$l$^$9!%(Bdest_encoding$B$O!$(B
  $BJQ498e$N%(%s%3!<%G%#%s%0L>$G$9!%(B

  $BNc$r<($7$^$9!%(B

  SELECT convert(text, EUC_JP) FROM unicode_tbl;

  $B$O!$(BUnicode$B$N%F!<%V%k(Bunicode_tbl$B$N(Btext$BNs$r(BEUC_JP$B$KJQ49$7$FJV$7$^$9!%(B

  7.3$B$G$O$5$i$K(BSQL$BI8=`$N(BCONVERT$B4X?t$,;H$($^$9!%(BSQL$BI8=`$N(BCONVERT$B$O(B
  PostgreSQL$B$N(BCONVERT$B$H5!G=$O$[$H$s$IF1$8$G$9$,!$8F$S=P$77A<0$,0[$j$^(B
  $B$9!%(B

  SELECT convert(text using euc_jp_to_utf8) FROM unicode_tbl;

  "using" $B$N8e$N0z?t$O!V%3%s%P!<%8%g%sL>!W$G$9!%$3$NNc$G$O!$(BEUC_JP $B$+(B
  $B$i(B UTF-8 $B$KJQ49$9$k%3%s%P!<%8%g%s$r;XDj$7$F$$$^$9!%Dj5A:Q$N%3%s%P!<(B
  $B%8%g%s$K$D$$$F$O!$%f!<%6!<%:%,%$%I$N(B "String Functions and
  Operators" $B$NI=(B"Built-in Conversions" $B$r8+$F$/$@$5$$!%(B

$B"#%(%s%3!<%G%#%s%0JQ49ITG=$N>l9g$N=hM}(B

  $B%P%C%/%(%s%IB&$N%(%s%3!<%G%#%s%0$H%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0(B
  $B$,$$$D$bAj8_JQ49$G$-$k$H$O8B$j$^$;$s!%6KC<$JOC!$%P%C%/%(%s%IB&$,(B 
  EUC_JP $B$J$N$K!$%U%m%s%H%(%s%IB&$,(B EUC_KR $B$@$C$?$i$I$&$J$k$G$7$g$&!%(B
  $B$3$N>l9g(B PostgreSQL $B$OJQ49$G$-$J$$%3!<%I$r(B 16$B?JI=8=$KJQ49$7$^$9!%(B
  $B$?$H$($P!$(B"(bdae)" $B$N$h$&$K!%$J$*!$$3$N(B 16$B?JI=8=$O(B mule
  internal code $B$N%3!<%I$G$"$k$3$H$KCm0U$7$F2<$5$$!%$3$l$O!$D>@\%U%m%s(B
  $B%H%(%s%I(B <--> $B%P%C%/%(%s%I$N%(%s%3!<%G%#%s%0$rJQ49$9$k$N$G$O$J$/!$0l(B
  $BEYFbItI=8=$G$"$k(B mule internal code $B$r7PM3$7$F$$$k$?$a$G$9!%(B

  $B$J$*!$(BUnicode$B$H$=$l0J30$N%(%s%3!<%G%#%s%0$NJQ49$@$1$ONc30$G!$(BNOTICE
  $B%a%C%;!<%8$,I=<($5$l!$JQ49ITG=$NJ8;z$OL5;k$5$l$^$9!%(B

$B"#%G%U%)%k%H%3%s%P!<%8%g%s(B

  $B%G%U%)%k%H%3%s%P!<%8%g%s$O!$%P%C%/%(%s%I$H%U%m%s%H%(%s%I$H$N4V$N%(%s(B
  $B%3!<%G%#%s%0$N<+F0JQ49$K;H$o$l$kFCJL$J%3%s%P!<%8%g%s$G$9!%%G%U%)%k%H(B
  $B%3%s%P!<%8%g%s$O3F!9$N(B{$B%9%-!<%^!$%=!<%9%(%s%3!<%G%#%s%0!$%G%9%F%#%M!<(B
  $B%7%g%s%(%s%3!<%G%#%s%0(B}$B$NAH$_9g$o$;$K$*$$$F!$$?$@0l8D$@$1B8:_$7$^$9!%(B
  $B>e5-$G@bL@$7$?AH$_9~$_:Q$N%3%s%P!<%8%g%s$O!$(Bpg_catalog$B%9%-!<%^$K$*$$(B
  $B$FDj5A$5$l$F$*$j!$%9%-!<%^%5!<%A%Q%9$N@_Dj$K4X$o$i$:I,$:MxMQ$G$-$k%3(B
  $B%s%P!<%8%g%s$K$J$C$F$$$^$9!%(B

  $B5U$K8@$&$H!$(B pg_catalog $B0J30$N%9%-!<%^$K%G%U%)%k%H%3%s%P!<%8%g%s$r:n(B
  $B@.$9$k$3$H$K$h$j!$%G%U%)%k%H%3%s%P!<%8%g%s$r<+M3$KA*Br$9$k$3$H$b$G$-(B
  $B$k$o$1$G$9!%$?$H$($P(B SJIS $B$H$NJQ49$K$*$$$F!$(BPostgreSQL $B$,MQ0U$7$F$$(B
  $B$k(B MS932$B8_49(B $B$NJQ49$G$O$J$/!$(BJIS $B5,3J$N%7%U%H%8%9$KAjEv$9$kJQ49$r9T(B
  $B$&$h$&$J%3%s%P!<%8%g%s$r:n@.$9$k$3$H$b2DG=$G$9!%(B

$B"#%f!<%6Dj5A%3%s%P!<%8%g%s$N:n@.(B

  PostgreSQL 7.3$B0J9_!$%f!<%6Dj5A$N%3%s%P!<%8%g%s$r:n@.$G$-$k$h$&$K$J$C(B
  $B$F$$$^$9!%%3%s%P!<%8%g%s$NDj5A$O(B CREATE CONVERSION $B$H$$$&(B SQL $B%3%^%s(B
  $B%I$r;H$C$F9T$$$^$9!%(B

    CREATE [DEFAULT] CONVERSION conversion_name
        FOR source_encoding
        TO dest_encoding FROM funcname

  $B>\:Y$O%j%U%!%l%s%9%^%K%e%"%k$r$4Mw2<$5$$!%(B

$B"#(BSJIS$B%f!<%6Dj5AJ8;z$X$NBP1~(B

  7.0 $B$+$i(B SJIS$B%f!<%6Dj5AJ8;z(B (UDC) $B$KBP1~$7$F$$$^$9!%(BUDC $B$r$I$&07$&$+(B
  $B$H8@$&$3$H$K$D$$$FCf>r$5$s(B(<EMAIL>)$B$+$iLdBjDs5/$H>\:Y$J2r@b$r(B
  $BD:$-$^$7$?$N$G!$;29M$N$?$a$K$3$N%I%-%e%a%s%H$N:G8e$KIU$1$F$*$-$^$9!%(B
  $B$^$?!$$3$NLdBj$K$D$$$F$O!$(BPostgreSQL$BF|K\8l%a!<%j%s%0%j%9%H$N(B 
  [pgsql-jp 12288] (1999/12/17$BIU(B)$B$H(B [pgsql-jp 12486] (2000/1/5$BIU(B) $B$+$i(B
  $B;O$^$k%9%l%C%I$G5DO@$r8+$k$3$H$,$G$-$^$9(B($B%a!<%k$N%"!<%+%$%V$O(B
  http://www.sra.co.jp/people/t-ishii/PostgreSQL/ $B$G;2>H$G$-$^$9(B)$B!%(B

  $B$3$3$G$O!$$=$l$i$N5DO@$r$U$^$(!$4JC1$K2r@b$7$^$9!%(B

  PostgreSQL$B$G$O!$F|K\8l$r;HMQ$9$k:]$K%P%C%/%(%s%IB&$N%(%s%3!<%G%#%s%0(B
  $B$r(B EUC_JP $B$^$?$O(B MULE_INTERNAL or Unicode $B$K$9$kI,MW$,$"$j$^$9!%(B
  MULE_INTERNAL $B$O(B EUC_JP $B$KJ8;z=89g$rI=$9%3!<%I$rIU$1$?$b$N$J$N$G!$K\(B
  $B<AE*$KF1$8$G$9!%$^$?!$(BUnicode <---> SJIS $BJQ49$O8=:_$N$H$3$m%5%]!<%H(B
  $B$5$l$F$$$^$;$s$N$GL5;k$7$^$9!%$7$?$,$C$F!$$3$3$G$O(B EUC_JP $B$H(B SJIS $B$N(B
  $BAj8_JQ49$N$_$r9M$($^$9!%(B

  $BM=HwCN<1(B

  $B0l8}$K(B EUC_JP $B$H$$$C$F$b!$<B:]$K$OCf?H$OJ#?t$NJ8;z=89g$+$i@.$jN)$C$F(B
  $B$$$^$9!%(B

	G0: JIS ROMAN (ASCII $B$H$[$\F1$8(B)
	G1: JIS X 0208 (JIS $B4A;z(B)
	G2: JIS X 0201 (1$B%P%$%H%+%J(B)
	G3: JIS X 0212 (JIS $BJd=u4A;z(B)

  $B0lJ}(B SJIS $B$O$3$N$&$A4pK\E*$K(B G0, G1, G2 $B$r%5%]!<%H$7$F$*$j!$(BG3 $B$O%5(B
  $B%]!<%H$7$F$$$^$;$s!%$7$?$,$C$F!$(BSJIS $B$O(B EUC_JP $B$NItJ,=89g$H$_$J$9$3(B
  $B$H$,$G$-!$<B:](B PostgreSQL 6.5 $B$^$G$O$3$N9M$($G<BAu$5$l$F$$$^$7$?!%(B

  $B$H$3$m$,!$(BWindows PC $B$N(B SJIS $B$N@$3&$G$O!$>e5-(B JIS $B5,3J$GDj5A$5$l$F$$(B
  $B$J$$J8;z%3!<%I$,0lItMxMQ$5$l$F$*$j!$$3$NItJ,(B (UDC) $B$O=>Mh(B PostgreSQL 
  $B$G$OA4$/9MN8$5$l$F$$$^$;$s$G$7$?!%<B:](B UDC $B$r4^$`(B SJIS $B$r(B EUC_JP $B$K(B
  $BJQ49$9$k$H$-$KIT@5$JJQ49$,9T$o$l$F$$$^$7$?!%$=$3$G(B PostgreSQL 7.0 $B$G(B
  $B$O!$$^$:$3$NLdBj$r2r7h$9$k$3$H$K$7$^$7$?!%(B

  $B$^$?!$(BUDC $B$NMxMQJ}$K$D$$$F$OI8=`5,3J$N$h$&$J$b$N$O$"$j$^$;$s$,!$<B$O(B
  $B6H3&CDBN$G$N<h$j7h$a$,$"$j!$$$$o$f$k%G%U%!%/%H%9%?%s%@!<%I$J$i$PB8:_(B
  $B$9$k$3$H$,J,$+$j$^$7$?!%$=$3$G$3$l$K$D$$$F$b$G$-$k$@$1%5%]!<%H$9$k$3(B
  $B$H$K$7$^$7$?!%(B

  PostgreSQL 7.0 $B$G$N(B UDC $BBP1~$N<BAu(B

  (1) $B%f!<%6Dj5AJ8;zNN0h$O(B JIS $B$N%f!<%6Dj5AJ8;zNN0h$K%^%C%T%s%0$9$k!%(B
  SJIS $B$H(B EUC_JP $B$G(B1$BBP(B1$B$NBP1~$K$J$j$^$9!%(B

    - SJIS $B%f!<%6Dj5AJ8;zNN0h(B A ($B2>>N(B)
          95$B!A(B104 $B6h(B  $B"+"*(B $BF|K\8l(B EUC / G1 (JIS X 0208) 85$B!A(B95 $B6h(B

    - SJIS $B%f!<%6Dj5AJ8;zNN0h(B B ($B2>>N(B)
         105$B!A(B114 $B6h(B  $B"+"*(B $BF|K\8l(B EUC / G3 (JIS X 0212) 85$B!A(B95 $B6h(B

  (2) IBM $B3HD%J8;zNN0h(B (SJIS 115$B!A(B120 $B6h(B)

  $BJQ49%F!<%V%k$K$h$C$F(B G1 (JIS X 0208)$B$H!$(BG3 (JIS X 0212)$B$KJQ49$5$l$^(B
  $B$9!%$J$*!$$3$NJQ49$K$*$$$F$O!$(BSJIS --> EUC_JP $B$GJQ49$7!$:F$S(B EUC_JP --
  > SJIS $B$KJQ49$9$k$H85$N(B SJIS $B$KLa$i$J$$$3$H$,$"$j$^$9!%$^$?!$(BEUC_JP --
  > SJIS $B$NJQ49$G$O!$$9$Y$F$NJ8;z$rJQ49$G$-$k$o$1$G$O$J$$$N$G!$$=$N>l(B
  $B9g$OJQ49ITG=J8;z$H$7$F!V".!W$KCV$-49$($^$9!%(B

  *$B6H3&CDBN$N<h$j7h$a$G$O!$JQ49ITG=J8;z$O!V<BAu0MB8!W$H$J$C$F$$$^$9$,!$(B
  Solaris $B$r$O$8$a!$B?$/$N%7%9%F%`$,!V".!W$rJQ49ITG=J8;z$K:NMQ$7$F$$$^(B
  $B$9!%(BPostgreSQL$B$b$3$l$K9g$o$;$^$7$?!%(B

  (3) NEC $BA*Dj(B IBM $B3HD%J8;zNN0h(B (SJIS 89$B!A(B92 $B6h(B)
  
  PostgreSQL 7.0$B$G$O$9$Y$FJQ49ITG=J8;z!V".!W$KCV$-49$($i$l$^$9!%(B

  PostgreSQL 7.0.1$B0J9_$G$O!$0lC6(B IBM $B3HD%J8;zNN0h$KJQ49$5$l$?8e!$(BG1
  (JIS X 0208)$B$H!$(BG3 (JIS X 0212)$B$KJQ49$5$l$^$9!%(B

$B<U<-!'(B

  o $BFA2H(B@$B;06(1?M"%5!<%S%9$5$s$+$i!$(BNEC $BA*Dj(B IBM $B4A;zBP1~%Q%C%A$rDs6!$7(B
    $B$F$$$?$@$-$^$7$?!%(B

  o $B3F<oJ8;z%;%C%H!$%3!<%I7O$K$D$$$F!$F|K\8l(B PostgreSQL $B%a!<%j%s%0%j%9%H(B
    $B$N%a%s%P$NJ}$+$i%"%I%P%$%9$rD:$-$^$7$?!%$3$3$K46<U$7$^$9!%(B
    $B$^$?!$(BSJIS $BBP1~$K$D$$$F$O!$;T@n(B@$B$*CcBg$5$s$N%Q%C%A$r;29M$K$5$;$F$$(B
    $B$?$@$-$^$7$?!%(B

  o SJIS$B%f!<%6Dj5AJ8;z(B (UDC) $B$r$I$&07$&$+$H8@$&$3$H$K$D$$$FCf>r$5$s(B
    (<EMAIL>)$B$+$iLdBjDs5/$H>\:Y$J2r@b$rD:$-$^$7$?!%(B

$B"#(BUnicode$B$H$=$l0J30$N%(%s%3!<%G%#%s%0$H$NAj8_JQ49$K$D$$$F(B

  PostgreSQL 7.1$B$+$i(BUnicode$B$H$=$l0J30$N%(%s%3!<%G%#%s%0$H$NAj8_JQ49$,(B
  $B2DG=$K$J$j$^$7$?!%$3$NJQ49$O$4$/0lIt$NJ8;z%3!<%I(B(ISO 8859-1)$B$r$N$>$-!$(B
  $B%m%8%C%/$K$h$kJQ49$,$G$-$J$$$?$a!$JQ49$N:]$K$O%F!<%V%k$,I,MW$K$J$j$^(B
  $B$9!%(BPostgreSQL$B$N<BAu$G$O!$(BUnicode$BJQ49%F!<%V%k$O(B Unicode organization 
  $B$,Ds6!$9$k$b$N$r;HMQ!$$3$l$r(BPerl$B%W%m%0%i%`$G(BC$B8@8l$N%F!<%V%k$KJQ49$7(B
  $B$F:n@.$7$F$$$^$9(B(Perl$B%W%m%0%i%`$O(BNARITA Tomio$B;a:n@.$N(Blv$B%P!<%8%g%s(B
  4.3.6 $B$KIUB0$9$k$b$N$r2~B$$N>e!$MxMQ$7$F$$$^$9(B)$B!%(BUnicode
  organization$B$NDs6!$9$kJQ49%F!<%V%k$O:FG[I[$,5v2D$5$l$F$$$J$$$?$a!$(B
  PostgreSQL$B$N%=!<%9%3!<%I$K$O4^$^$l$F$$$^$;$s!%0J2<!$;HMQ$7$?JQ49%F!<(B
  $B%V%k$rNs5s$7$^$9!%(B

  $B%(%s%3!<%G%#%s%0(B	$BJQ49%F!<%V%k(B
  ============================================================
  ISO 8859-1		$B$J$7(B
  ISO 8859-2		8859-2.TXT
  ISO 8859-3		8859-3.TXT
  ISO 8859-4		8859-4.TXT
  ISO 8859-5		8859-5.TXT
  ISO 8859-6		8859-6.TXT
  ISO 8859-7		8859-7.TXT
  ISO 8859-8		8859-8.TXT
  ISO 8859-9		8859-9.TXT
  ISO 8859-10		8859-10.TXT
  ISO 8859-13		8859-13.TXT
  ISO 8859-14		8859-14.TXT
  ISO 8859-15		8859-15.TXT
  ISO 8859-16		8859-16.TXT
  EUC_JP		JIS0201.TXT, JIS0208.TXT, JIS0212.TXT,
			CP932.TXT, sjis.map
  SJIS			CP932.TXT
  EUC_CN		GB2312.TXT
  GBK			CP936.TXT
  EUC_KR		KSX1001.TXT
  UHC			CP949.TXT
  JOHAB			JOHAB.TXT
  EUC_TW		CNS11643.TXT
  Big5			BIG5.TXT
  WIN1256		CP1256.TXT
  TCVN			CP1258.TXT
  WIN874		CP874.TXT
  ============================================================

$B<U<-!'(B

  o $BFA2H(B@$B;06(1?M"%5!<%S%9$5$s$+$i!$(BCP932.TXT$B$h$j@8@.$7$?(BSJIS$BMQ$NJQ49%F!<(B
    $B%V%k$rDs6!$7$F$$$?$@$-$^$7$?!%$3$l$K$h$j!$(BIBM $B3HD%J8;zNN0h(B (SJIS
    115$B!A(B120 $B6h(B), NEC $BA*Dj(B IBM $B3HD%J8;zNN0h(B (SJIS 89$B!A(B92 $B6h(B)$B$KBP1~$9$k(B
    $B$3$H$,$G$-$k$h$&$K$J$j$^$7$?!%(B


$B;29M(B1$B!'(B

  Pavel Behal$B;a$K$h$jDs6!$5$l$?(BWIN1250$B%5%]!<%H$G$9$,!$(BWindows$B4D6-$G$N(B
  $BMxMQ$N;EJ}$K$D$$$F;29M$K$J$k%I%-%e%a%s%H$,IUB0$7$F$$$k$N$G!$$3$3$KE:(B
  $BIU$7$F$*$-$^$9!%(B

  -------------------------------------------------------------------
Version: 0.91 for PgSQL 6.5
Author: Pavel Behal
Revised by: Tatsuo Ishii
Email: <EMAIL>
Licence: The Same as PostgreSQL

Sorry for my Eglish and C code, I'm not native :-)

!!!!!!!!!!!!!!!!!!!!!!!!! NO WARRANTY !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

Instalation:
------------
1) Change three affected files in source directories 
    (I don't have time to create proper patch diffs, I don't know how)
	[PostgreSQL 6.5.1$B$G$O$3$N%9%F%C%W$OI,MW$"$j$^$;$s!%(B-- $B@P0f(B]
2) Compile with enabled locale and multibyte set to LATIN2
3) Setup properly your instalation, do not forget to create locale
   variables in your profile (environment). Ex. (may not be exactly true):
	LC_ALL=cs_CZ.ISO8859-2
	LC_COLLATE=cs_CZ.ISO8859-2
	LC_CTYPE=cs_CZ.ISO8859-2
	LC_MONETARY=cs_CZ.ISO8859-2
	LC_NUMERIC=cs_CZ.ISO8859-2
	LC_TIME=cs_CZ.ISO8859-2
4) You have to start the postmaster with locales set!
5) Try it with Czech language, it have to sort
5) Install ODBC driver for PgSQL into your M$ Windows
6) Setup properly your data source. Include this line in your ODBC
   configuration dialog in field "Connect Settings:" :
	SET CLIENT_ENCODING = 'WIN1250';
7) Now try it again, but in Windows with ODBC.

Description:
------------
- Depends on proper system locales, tested with RH6.0 and Slackware 3.6,
  with cs_CZ.iso8859-2 loacle
- Never try to set-up server multibyte database encoding to WIN1250,
  always use LATIN2 instead. There is not WIN1250 locale in Unix
- WIN1250 encoding is useable only for M$W ODBC clients. The characters are
  on thy fly re-coded, to be displayed and stored back properly
 
Important:
----------
- it reorders your sort order depending on your LC_... setting, so don't be
  confused with regression tests, they don't use locale
- "ch" is corectly sorted only in some newer locales (Ex. RH6.0)
- you have to insert money as '162,50' (with comma in aphostrophes!)
- not tested properly
  -------------------------------------------------------------------

$B;29M(B2$B!'(BSJIS$B%f!<%6Dj5AJ8;z(B (UDC) $B$r$I$&07$&$+$H8@$&$3$H$K$D$$$FCf>r$5$s(B
    (<EMAIL>)$B$+$i$$$?$@$$$?LdBjDs5/$H2r@b$G$9!%(B

-------------------------- $B0zMQ3+;O(B ----------------------------------
---
1. SJIS $B%3!<%I$NHO0O(B

    1 $B%P%$%HL\(B 0x81 - 0x9F$B!$(B0xE0 - 0xFC
    2 $B%P%$%HL\(B 0x40 - 0x7E$B!$(B0x80 - 0xFC

    $B$$$o$f$k!V30;zNN0h!W$NHO0O(B:

    - X0208 $B6&DL<+M3NN0h(B

    |--------------------
    | 85 $B6h(B  0xEB40 $B!A(B
    |...
    |--------------------
    | 89 $B6h(B  0xED40 $B!A(B    ; 89$B!A(B92 $B6h$O(B
    |...                  ; $B!V(BNEC $BA*Dj(B IBM $B3HD%J8;zNN0h!W(B
    |-------------------- ; $B$H8F$P$l$k(B
    | 93 $B6h(B  0xEF40 $B!A(B
    | 94 $B6h(B  0xEF9F $B!A(B 0xEFFC

    - $B%f!<%6Dj5AJ8;zNN0h(B
    
    |--------------------
    | 95 $B6h(B  0xF040 $B!A(B    ; 95$B!A(B104 $B6h(B
    |...                  ; $B!V%f!<%6Dj5AJ8;zNN0h(B A$B!W(B($B2>>N(B)
    |--------------------
    |105 $B6h(B  0xF540 $B!A(B    ; 105$B!A(B114 $B6h(B
    |...                  ; $B!V%f!<%6Dj5AJ8;zNN0h(B B$B!W(B($B2>>N(B)
    |--------------------
    |115 $B6h(B  0xFA40 $B!A(B    ; 115$B!A(B120 $B6h$O0lHL$K(B
    |...                  ; $B!V(BIBM $B3HD%J8;zNN0h!W(B
    |120 $B6h(B  ...          ; $B$H8F$P$l$k(B
    |--------------------

---
2. i-mode $BC<Kv$,;H$C$F$$$k?^7AJ8;z%3!<%I$NHO0O(B

    0xF89F - 0xF8FC  (112 $B6h(B)
    0xF940 - 0xF949  (113 $B6h(B)
    0xF972 - 0xF97E  (113 $B6h(B)
    0xF980 - 0xF990  (113 $B6h(B)
    0xF9B0           (114 $B6h(B)

---
3. $B0lHLE*$J(B EUC $BF|K\8l%3!<%I$NDj5A(B

    G0 : [0x21-0x7E]                  ; $B$$$o$f$k(B JIS ROMAN
    G1 : [0xA1-0xFE] [0xA1-0xFE]      ; JIS X 0208 
    G2 : 0x8E [0xA1-0xDF]             ; JIS X 0201 $B%+%J(B
    G3 : 0x8F [0xA1-0xFE] [0x21-0x7E] ; JIS X 0212 $BJd=u4A;z(B

---
[$BLdBjE@(B]

SJIS 95$B!A(B120 $B6h$O(B JIS X0208 $B$K3:Ev$9$kNN0h$,B8:_$7$J$$(B
$B$?$a!$$3$NNN0h$N(B EUC - SJIS $BJ8;z%3!<%IJQ49$O3F%Y%s%@$K(B
$B$h$C$F0[$J$k$N$G$O$J$$$+!$$H$$$&$N$,@P0fMM$+$i$N$4;XE&(B
$B$G$7$?!%(B

---
[$B5DO@(B]

$BD4::$N7k2L!$(BSJIS 95$B!A(B120 $B6h$r(B EUC $B$KJQ49$9$k$?$a$NI8=`E*$J(B
$B%k!<%k$,$J$$$o$1$G$O$J$$!$$H$$$&$3$H$,$o$+$j$^$7$?!%>\:Y$O(B
$B8e=R$N;29M;qNA$r$4Mw$$$?$@$/$H$7$F!$$3$3$G$O$=$N%k!<%k$r(B
$B4JC1$K$4@bL@$$$?$7$^$9!%(B

   - SJIS $B%f!<%6Dj5AJ8;zNN0h(B A ($B2>>N(B)
          95$B!A(B104 $B6h(B  $B"+"*(B $BF|K\8l(B EUC / G1 85$B!A(B95 $B6h(B

         $B$?$H$($P(B SJIS $B$N(B (95, 1) = 0xF040 $B$O(B
         EUC $B$N(B 0xF5A1 $B$K$J$j$^$9!%(B

   - SJIS $B%f!<%6Dj5AJ8;zNN0h(B B ($B2>>N(B)
         105$B!A(B114 $B6h(B  $B"+"*(B $BF|K\8l(B EUC / G3 85$B!A(B95 $B6h(B

         $B$?$H$($P(B SJIS $B$N(B (105, 1) = 0xF540 $B$O(B
         EUC $B$N(B 0x8FF5A1 $B$K$J$j$^$9!%(B

   - IBM $B3HD%J8;zNN0h(B
         115$B!A(B120 $B6h(B

         JIS X 0208 ($BF|K\8l(B EUC / G1)$B!$(BJIS X 0212 
         ($BF|K\8l(B EUC / G3) $B$K3:Ev$9$kJ8;z$,$"$k>l9g(B
         $B$O$=$NJ8;z$K%^%C%T%s%0!%$=$&$G$J$$>l9g$O(B
         $BF|K\8l(B EUC / G3 83$B!A(B84 $B6h$r!$6hE@%3!<%I$N>e0L(B
         $B$+$i=g$K3d$jEv$F$F$$$/(B ($BJQ49%F!<%V%kJ}<0(B)

$B$3$N;EMM$O!$9-$/;H$o$l$F$$$k(B SJIS $B$H(B EUC $B$N%^%C%T%s%0$,%Y%s%@$K(B
$B$h$C$F0[$J$k$?$a!$Aj8_1?MQ$N:]$KLdBj$K$J$C$F$$$k$3$H$+$i!$(B1996 
$BG/$K(B OSF $BF|K\%Y%s%@6(5D2q$,8!F$:n@.$7$?Js9p=q$,%Y!<%9$K$J$C$F$$(B
$B$k$h$&$G$9!%(B

Solaris $B$N%I%-%e%a%s%H$K$O!V(BTOG $BF|K\%Y%s%@6(5D2q?d>)(B EUC$B!&%7%U%H(B 
JIS $B%3!<%IJQ49;EMM!W$K$b$H$E$/$H=q$$$F$"$j!$(BSolaris 2.6 $B$+$iF3F~(B
$B$7$F$$$k$N$@$=$&$G!$;d$+$i8+$l$P;v<B>e$NI8=`$H9M$($F$bIT<+A3$G$O(B
$B$J$$$H46$8$^$9!%(B

$B$J$*!$>/$J$/$H$b(B 1996 $BG/Ev;~$K$*$$$F$O!$(BOracle $B$d(B Sybase $B$O(B 
SJIS $B$N%f!<%6Dj5A(B/$B%Y%s%@Dj5AJ8;zNN0h$r(B EUC $B$KJQ49$9$k:]!$H=JLIT(B
$B2DG=J8;z$H$7$F07$C$F$$$k$i$7$$$H$$$&$3$H$bJdB-$7$F$*$-$^$9!%(B

---
[$B;29M;qNA(B]

// URL $B$,D9$$$N$G!$ESCf$G@Z$l$J$$$H$$$$$N$G$9$,(B...

-$B!VF|K\8l(B EUC$B!&%7%U%H(B JIS $B%3!<%IJQ49;EMM$H%3!<%I7O<BBVD4::!W(B
    1966, OSF $BF|K\%Y%s%@6(5D2q(B
    http://www.opengroup.or.jp/jvc/cde/sjis-euc.html

-$B!VJ8;z%3!<%IJQ495,B'!W(B
    Solaris 7$B!$(BJFP $B%f!<%6!<%:%,%$%I(B
    http://docs.sun.com/ab2/coll.139.3/JFPUG/@Ab2PageView/11683?Ab2Lang=ja&Ab2Enc=euc-jp

-$B!VF|K\8lJ8;z%3!<%I!W(B
    Solaris 7$B!$(BJFP $B%f!<%6!<%:%,%$%I(B
    http://docs.sun.com/ab2/coll.139.3/JFPUG/@Ab2PageView/879;td=5?Ab2Lang=ja&Ab2Enc=euc-jp

    // $BFf$N!V(B1$B!A(B20 $B6h!W$N5-=R$O$3$3$+$i$-$F$$$^$9!%(B

---
-------------------------- $B0zMQ$3$3$^$G(B ---------------------------------

$B2~DjMzNr!'(B

  2002/10/21
	* $B%^%k%A%P%$%HBP1~$,%*%W%7%g%s$G$O$J$/!$8GDj$GI,$:AH$_9~$^$l$k(B
	  $B$h$&$K$J$j$^$7$?!%(B

	* CREATE CONVERSION/DROP CONVERSION$B$NDI2C!%$3$l$K$H$b$J$$!$%((B
	  $B%s%3!<%G%#%s%0JQ494X?t$,%m!<%@%V%k4X?t$K$J$j!$%P%C%/%(%s%I$N(B
	  $B%m!<%I%b%8%e!<%k%5%$%:$,(B7.2$B$h$j$b>.$5$/$J$C$F$$$^$9!%$^$?!$(B
	  SQL$BI8=`$N(BCONVERT$B4X?t$rDI2C$7$^$7$?!%(B
	* $B$$$/$D$+%(%s%3!<%G%#%s%0$,DI2C$5$l$F$$$^$9!%(B
	* $B0J>e!$(B7.3$B$KH?1G$5$l$^$9!%(B

  2001/10/01
	* CONVERT$B$NDI2C!%(Blpad/rpad/trim/btrim/ltrim/rtrim/translate$B$N(B
	  $B%^%k%A%P%$%HBP1~DI2C!%(Bchar/varchar$B$G%P%$%H?t$G$O$J$/!$J8;z?t(B
	  $B$G%5%$%:$rDj5A$9$k$h$&$KJQ99!%0J>e!$(B7.2$B$KH?1G$5$l$^$9!%(B

  2001/2/15
	* $BFA2H(B@$B;06(1?M"%5!<%S%9$5$s$+$i!$(BCP932.TXT$B$h$j@8@.$7$?(BSJIS$BMQ$N(B
	  $BJQ49%F!<%V%k$rDs6!$7$F$$$?$@$-$^$7$?!%(B7.1$B$KH?1G$5$l$^$9!%(B

  2001/1/6
	* UNICODE$B$HB>$N%(%s%3!<%G%#%s%0$H$NAj8_JQ495!G=$rDI2C!%(B
	* 7.1$B$KH?1G$5$l$^$9!%(B

  2000/5/20
	* NEC $BA*Dj(B IBM $B4A;zBP1~$rDI2C$7$^$7$?!%$3$l$O(B $BFA2H(B@$B;06(1?M"%5!<%S%9(B
	  $B$5$s$+$i$N(B contribute $B$G$9!%(B
	* $B$3$l$i$O!$(B7.0.1 $B$KH?1G$5$l$^$9!%(B

  2000/3/22
	* PQsetClientEncoding, PQclientEncoding $B$r(Blibpq $B4X?t$KDI2C!$(B
	  $B%3%M%/%7%g%sKh$K%(%s%3!<%G%#%s%0$rJQ992DG=$K!%(B
	* SJIS $B%f!<%6Dj5AJ8;z(B (UDC) $B$X$NBP1~(B
  	* ./configure --with-mb=EUC_JP $B$+$i(B
	  ./configure --enable-multibyte=EUC_JP $B$KJQ99(B
  	* SQL_ASCII $B$N(B regression test $BDI2C(B
	* $B$3$l$i$O(B 7.0 $B$KH?1G$5$l$^$9!%(B

  1999/7/11 WIN1250(Windows$BMQ$N%A%'%38l(B)$B%5%]!<%H$rDI2C$7$^$7$?!%(B
	* WIN1250 $B$,%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0$H$7$FMxMQ$G$-$k$h(B
	  $B$&$K$J$j$^$7$?!%$3$N>l9g!$%P%C%/%(%s%IB&$N%(%s%3!<%G%#%s%0$O(B
	  LATIN2 $B$^$?$O(B MULE_INTERNAL $B$H$7$^$9!%(B
	  (contributed by Pavel Behal)
	* backend/utils/mb/conv.c$B$K$*$1$k7?$NIT@09g$r=$@5$7$^$7$?!%(B
	  (contributed by Tomoaki Nishiyama)
	* $B$3$l$i$O(B6.5.1$B$KH?1G$5$l$^$9!%(B

  1999/3/23 $B%-%j%kJ8;z%5%]!<%HDI2CB>(B(6.5 $B$KH?1G:Q(B)
	* $B%(%s%3!<%G%#%s%0$H$7$F(B KOI8(KOI8-R), WIN(CP1251), ALT(CP866) $B$r(B
	  $B%5%]!<%H$7$F$$$^$9!%$3$l$i$O!$%U%m%s%H%(%s%I!$%P%C%/%(%s%I!$(B
	  $B$I$A$i$N%(%s%3!<%G%#%s%0$H$7$F$b;HMQ2DG=$G$"$j!$%(%s%3!<%G%#%s%0$N(B
	  $BAj8_JQ49$,2DG=$G$9!%$^$?!$=>Mh$+$i%5%]!<%H$7$F$$$k(B ISO 8859-5 $B$b(B
	  $BF1MM$K;HMQ2DG=$G$9!%(B
	  $B%-%j%kJ8;z%5%]!<%H$O!$(BOleg Broytmann <<EMAIL>> $B;a$N(B
	  $B%j%/%(%9%H5Z$S6(NO$K$h$j<B8=$7$^$7$?!%$3$l$O!$=>Mh$+$i$"$k(B
	  RCODE $B%5%]!<%H$N5!G=$r<h$j9~$`$b$N$G$b$"$j$^$9!%(B
	* MB $B$H(B locale $B$rF1;~$K;XDj$7$?>l9g$KBgJ8;z!?>.J8;z$rL5;k$7$?(B
	  $B@55,I=8=8!:w$,@5>o$KF0:n$7$J$$%P%0$r=$@5(B

  1999/1/26 Big5 $B%5%]!<%HDI2C(B(6.4.2-patched/6.5 $B$KH?1G:Q(B)
	* Big5 $B$,%U%m%s%H%(%s%IB&$N%(%s%3!<%G%#%s%0$H$7$FMxMQ$G$-$k$h(B
	  $B$&$K$J$j$^$7$?!%$3$N>l9g!$%P%C%/%(%s%IB&$N%(%s%3!<%G%#%s%0$O(B
	  EUC_TW $B$^$?$O(B MULE_INTERNAL $B$H$7$^$9!%(B
	* EUC_TW $B$N(B regression test $B%1!<%9$rDI2C(B
	  (contributed by Jonah Kuo <<EMAIL>>)

  1998/12/16 $BK\%I%-%e%a%s%H=$@5(B(6.4.2 $B$KH?1G:Q(B)$B!%(B
	* Makefile.custom $B$G(B MB=EUC_JP $B$J$I$H@_Dj$9$kJ}K!$O(B 6.4 $B0J9_(B
	  $B%5%]!<%H$5$l$F$$$J$$$N$G:o=|$7$?!%(B
	* $BJ8;z%3!<%I(B $B"*(B $B%(%s%3!<%G%#%s%0!$%/%i%$%"%s%H"*%U%m%s%H%(%s%I(B
	  $B%5!<%P"*%P%C%/%(%s%I(B $B$K$=$l$>$l8l6g$r=$@5!%(B

  1998/12/15 6.4 $B8~$1%P%0=$@5%Q%C%A%j%j!<%9(B(6.4.2 $B$KH?1G:Q(B)$B!%(B
	* SQL_ASCII $B%5%]!<%H$N%P%0=$@5(B

  1998/11/21 6.4 $B8~$1%P%0=$@5%Q%C%A%j%j!<%9(B(6.4.2 $B$KH?1G:Q(B)$B!%(B
	* BINARY CURSOR $B$NLdBj$r=$@5(B
	* pg_dumpall $B$N%P%0=$@5(B

  1998/11/5 6.4 $B%j%j!<%9!%(B
	* pg_database $B$N(B encoding $B%+%i%`$,(B MB $B$,M-8z$G$J$$$H$-$K$b(B
	  $BDI2C$5$l$k$h$&$K$J$C$?!%$=$N$?$a!$(BMB $B$,M-8z$G$J$$$H$-$K$O!$(B
	  ASCII $B$N%(%s%3!<%G%#%s%0$rI=$9(B SQL_ASCII $B$r?7$7$$%(%s%3!<%G%#%s%0(B
	  $B$H$7$FDI2C$7$?!%$3$l$K$H$b$J$$!$%(%s%3!<%G%#%s%0L>$KBP1~$9$k(B
	  $B%(%s%3!<%G%#%s%0(BID$B$,(B SQL_ASCII $B$r(B 0 $B$H$9$kHV9f$KJQ99$K$J$C$?!%(B

  1998/7/22 6.4 $B&A8~$1$K%Q%C%A$r%j%j!<%9!%(B
	* initdb/createdb/create database $B$G%P%C%/%(%s%IB&$N(B
	  $B%(%s%3!<%G%#%s%0$r@_Dj$-$k5!G=<BAu!%$3$N$?$a!$%7%9%F%`%+%?%m(B
	  $B%0$N(B pg_database $B$K?7$7$$%+%i%`(B encoding $B$rDI2C(B(MB$B$,M-8z$J;~$@$1(B)
	* copy $B$,(B PGCLIENTENCODING $B$KBP1~(B
	* SQL92 $B$N(B "SET NAMES" $B$r%5%]!<%H(B(MB$B$,M-8z$J;~$@$1(B)
	* LATIN2-5 $B$r%5%]!<%H(B
	* regression test $B$K(B unicode $B$N%F%9%H%1!<%9$rDI2C(B
	* MB $B@lMQ$N(B regression $B%F%9%H%G%#%l%/%H%j(B test/mb $B$rDI2C(B
	* $B%=!<%9%U%!%$%k$NCV$->l=j$rBgI}8+D>$7!%(BMB $B4X78$O(B
	  include/mb, backend/utils/mb $B$KCV$/$h$&$K$7$?(B

  1998/5/25 $B%P%0=$@5(B(mb_b3.patch $B$H$7$F(B pgsql-jp ML $B$K%j%j!<%9!$(B
	$BK\2H$G$O(B 6.4 snapshot $B$K<h$j9~$^$l$kM=Dj(B)	

  1998/5/18 $B5!G=DI2C!?%P%0=$@5(B(mb_b2.patch $B$H$7$F(B pgsql-jp ML $B$K%j%j!<%9!$(B
	$BK\2H$G$O(B 6.4 snapshot $B$K<h$j9~$^$l$kM=Dj(B)
	* $B4D6-JQ?t(B PGCLIENTENCODING $B$N%5%]!<%H!%%U%m%s%H%(%s%IB&$N(B
	  $B%(%s%3!<%G%#%s%0$r;XDj$9$k!%8=:_!$(BSJIS, EUC_*, MULE_INTERNAL, 
	  LATIN1 $B$,;XDj$G$-$k!%$^$?!$(B
	  set client_encoding to 'sjis';
	  $B$G$b2DG=(B
	* 8bit $BJ8;z$,EO$k$HLdBj$,5/$-$k2U=j$K$G$-$k$@$1BP1~(B

  1998/4/21 $B5!G=DI2C!?%P%0=$@5(B(mb_b1.patch $B$H$7$F(B pgsql-jp ML $B$K%j%j!<%9!$(B
	$BK\2H$G$O(B 6.4 snapshot $B$K<h$j9~$^$l$F$$$k(B)
	* character_length(), position(), substring() $B$N%^%k%A%P%$%H(B
	  $BBP1~(B
	* octet_length() $BDI2C(B $B"*(B initdb $B$N$d$jD>$7I,MW(B
	* configure $B$N%*%W%7%g%s$K(B MB $B%5%]!<%HDI2C(B
	  (ex. configure --with-mb=EUC_JP)
	* EUC_KR $B$N(B regression test $BDI2C(B
	  ("Soonmyung. Hong" <<EMAIL>> $B$5$sDs6!(B)
	* EUC_JP $B$N(B regression test $B$K(B character_length(), position(),
	  substring(), octet_length() $BDI2C(B
	* regress.sh $B$N(B SystemV $B$K$*$1$kHs8_49@-=$@5(B
	* toupper(), tolower() $B$K(B 8bit $BJ8;z$,EO$k$HMn$A$k$3$H$,(B
	  $B$"$k$N$r=$@5(B

  1998/3/25 PostgreSQL 6.3.1 $B%j%j!<%9!$(BMB PL2 $B$,<h$j9~$^$l$k(B

  1998/3/10 PL2 $B$r%j%j!<%9(B
	* EUC_JP, EUC_CN, MULE_INTERNAL $B$N(B regression test $B$rDI2C(B
	  (EUC_CN $B$N%G!<%?$O(B <EMAIL> $B$5$sDs6!(B)
	* regexp $B$K$*$$$F!$(Bisalpha $B$J$I$K(B unsigend char $B0J30$NCM$,(B
          $BEO$i$J$$$h$&$K%,!<%I$r$+$1$k(B
	* $B1Q8l$N%I%-%e%a%s%H$rDI2C(B
	* MB $B$rDj5A$7$J$$>l9g$KH/@8$9$k%P%0$r=$@5(B

  1998/3/1 PL1 $B$r%j%j!<%9(B

$B0J>e!%(B
